import { ClineMessage } from "@shared/ExtensionMessage"
import { QaxTodoItem, QaxTodoStatus } from "../components/todo/types/QaxTodoTypes"

/**
 * Parse markdown checklist to QaxTodoItem array
 * @param md Markdown checklist string
 * @returns Array of QaxTodoItem objects
 */
function parseQaxMarkdownChecklist(md: string): QaxTodoItem[] {
	if (typeof md !== "string") return []
	const lines = md
		.split(/\r?\n/)
		.map((l) => l.trim())
		.filter(Boolean)
	const todos: QaxTodoItem[] = []
	for (const line of lines) {
		const match = line.match(/^\[\s*([ xX\-~])\s*\]\s+(.+)$/)
		if (!match) continue
		let status: QaxTodoStatus = "pending"
		if (match[1] === "x" || match[1] === "X") status = "completed"
		else if (match[1] === "-" || match[1] === "~") status = "in_progress"
		// ID 只基于任务内容生成，不包含状态，确保状态变化时 ID 不变
		const id = generateQaxTodoId(match[2])
		todos.push({
			id,
			content: match[2],
			status,
		})
	}
	return todos
}

/**
 * Generate a simple ID for Qax todo items
 * @param input Input string to generate ID from
 * @returns Generated ID string
 */
function generateQaxTodoId(input: string): string {
	// Simple hash function for browser environment
	let hash = 0
	for (let i = 0; i < input.length; i++) {
		const char = input.charCodeAt(i)
		hash = (hash << 5) - hash + char
		hash = hash & hash // Convert to 32bit integer
	}
	return Math.abs(hash).toString(16)
}

/**
 * Extract the latest Qax todo list from ClineMessage history
 * @param clineMessages Array of ClineMessage objects
 * @returns Array of QaxTodoItem objects or empty array if none found
 */
export function getLatestQaxTodo(clineMessages: ClineMessage[]): QaxTodoItem[] {
	// 只处理完整的消息，排除 partial 消息以避免处理不完整的数据
	// 现在后端会在工具执行完成后发送一个 partial=false 的完整消息
	const relevantMessages = clineMessages.filter(
		(msg) =>
			((msg.type === "ask" && msg.ask === "tool") ||
				(msg.type === "say" && msg.say === "tool") ||
				(msg.type === "say" && msg.say === "qax_user_edit_todos")) &&
			!msg.partial, // 只处理完整消息
	)

	const parsedMessages = relevantMessages.map((msg) => {
		try {
			const parsed = JSON.parse(msg.text ?? "{}")
			return parsed
		} catch (error) {
			console.log("[qax-todolist] Failed to parse message:", msg.text?.substring(0, 100))
			return null
		}
	})

	const todoMessages = parsedMessages.filter((item) => {
		if (!item || item.tool !== "qaxUpdateTodoList") return false
		// Support both array format (new) and string format (legacy)
		const isValid = Array.isArray(item.todos) || typeof item.todos === "string"
		return isValid
	})

	const todos = todoMessages
		.map((item) => {
			// Convert string format to array format for consistency
			if (typeof item.todos === "string") {
				return parseQaxMarkdownChecklist(item.todos)
			}
			return item.todos
		})
		.pop()

	if (todos && Array.isArray(todos) && todos.length > 0) {
		return todos
	} else {
		return []
	}
}
