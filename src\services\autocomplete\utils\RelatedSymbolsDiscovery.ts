import * as vscode from "vscode"
import * as path from "path"
import * as fs from "fs/promises" // 导入 fs/promises 模块
import { getAst, getImportQuery } from "../../tree-sitter/languageParser"
import { executeRipgrepForFiles } from "../../search/file-search"
import { getBinPath } from "../../ripgrep"

// 为了在 Node.js 环境中测试，添加一些兼容性处理
const isNodeEnvironment = typeof window === "undefined" && typeof process !== "undefined"

/**
 * 符号定义信息
 */
export interface SymbolDefinition {
	name: string
	type: "function" | "class" | "interface" | "variable" | "type" | "constant" | "method" | "property"
	filePath: string
	range: vscode.Range
	content: string
	score: number
	source: "import" | "undefined_symbol" | "heuristic" | "recent_modified"
}

/**
 * 导入信息
 */
interface ImportInfo {
	modulePath: string
	symbols: string[]
	isDefault: boolean
	range: vscode.Range
}

/**
 * 未定义符号信息
 */
interface UndefinedSymbol {
	name: string
	range: vscode.Range
	type: string
}

/**
 * 文件修改信息
 */
interface FileModificationInfo {
	path: string
	lastModified: number
}

/**
 * 获取与指定文件最相关的符号定义
 * @param currentFilePath 当前文件路径
 * @param currentCode 当前文件代码内容
 * @param maxResults 最大返回结果数量，默认20
 * @returns 按相关性排序的符号定义数组
 */
export async function discoverRelatedSymbols(
	currentFilePath: string,
	currentCode: string,
	maxResults: number = 20,
): Promise<SymbolDefinition[]> {
	const strategies = [
		// 1. 直接导入分析 (权重: 高)
		await findRelatedSymbolsFromImports(currentFilePath, currentCode),

		// 2. 未定义符号分析 (权重: 高)
		await searchSymbolDefinitions(await findUndefinedSymbols(currentFilePath, currentCode)),

		// 3. 文件路径启发式 (权重: 中)
		await extractSymbolsFromFiles(await findRelatedFilesByHeuristic(currentFilePath)),

		// 4. 最近修改文件 (权重: 低)
		await extractSymbolsFromFiles((await findRecentlyModifiedRelated(currentFilePath)).map((f) => f.path)),
	]

	// 合并和去重
	return mergeAndRankSymbols(strategies, maxResults)
}

/**
 * 合并和排序符号
 */
function mergeAndRankSymbols(symbolSets: SymbolDefinition[][], maxResults: number): SymbolDefinition[] {
	const symbolMap = new Map<string, SymbolDefinition>()

	symbolSets.forEach((symbols, strategyIndex) => {
		const weight = [1.0, 0.9, 0.6, 0.3][strategyIndex] // 不同策略的权重

		symbols.forEach((symbol) => {
			const key = `${symbol.name}:${symbol.type}:${symbol.filePath}`
			if (symbolMap.has(key)) {
				const existing = symbolMap.get(key)!
				existing.score += weight
			} else {
				symbolMap.set(key, { ...symbol, score: weight })
			}
		})
	})

	// 按分数排序，返回前N个最相关的符号
	return Array.from(symbolMap.values())
		.sort((a, b) => b.score - a.score)
		.slice(0, maxResults)
}

/**
 * 从导入语句中查找相关符号
 */
async function findRelatedSymbolsFromImports(filePath: string, code: string): Promise<SymbolDefinition[]> {
	const symbols: SymbolDefinition[] = []

	try {
		// 使用 tree-sitter 解析导入语句
		const ast = await getAst(filePath, code)
		if (!ast) return symbols

		const importQuery = await getImportQuery(filePath)
		if (!importQuery) return symbols

		const matches = importQuery.matches(ast.rootNode)
		const imports = await extractImportsFromMatches(matches, filePath)

		// 对每个导入的模块，尝试解析其符号定义
		for (const importInfo of imports) {
			const moduleSymbols = await resolveImportedSymbols(importInfo, filePath)
			symbols.push(...moduleSymbols.map((s) => ({ ...s, source: "import" as const })))
		}
	} catch (error) {
		console.warn("Error finding symbols from imports:", error)
	}

	return symbols
}

/**
 * 从 tree-sitter 匹配结果中提取导入信息
 */
async function extractImportsFromMatches(matches: any[], filePath: string): Promise<ImportInfo[]> {
	const imports: ImportInfo[] = []

	for (const match of matches) {
		try {
			const captures = match.captures
			let modulePath = ""
			const symbols: string[] = []
			let isDefault = false
			let range: vscode.Range | undefined

			for (const capture of captures) {
				const node = capture.node
				const captureName = capture.name

				if (captureName === "module" || captureName === "source") {
					modulePath = node.text.replace(/['"]/g, "")
				} else if (captureName === "name" || captureName === "imported") {
					symbols.push(node.text)
				} else if (captureName === "default") {
					isDefault = true
					symbols.push(node.text)
				}

				if (!range) {
					range = new vscode.Range(
						new vscode.Position(node.startPosition.row, node.startPosition.column),
						new vscode.Position(node.endPosition.row, node.endPosition.column),
					)
				}
			}

			if (modulePath && range) {
				imports.push({
					modulePath,
					symbols: symbols.length > 0 ? symbols : ["*"],
					isDefault,
					range,
				})
			}
		} catch (error) {
			console.warn("Error extracting import from match:", error)
		}
	}

	return imports
}

/**
 * 解析导入模块的符号定义
 */
async function resolveImportedSymbols(importInfo: ImportInfo, currentFilePath: string): Promise<SymbolDefinition[]> {
	const symbols: SymbolDefinition[] = []

	try {
		// 解析模块路径
		const resolvedPath = await resolveModulePath(importInfo.modulePath, currentFilePath)
		if (!resolvedPath) return symbols

		// 读取模块文件内容
		const moduleContent = await fs.readFile(resolvedPath, "utf-8")

		// 提取模块中的符号定义
		const moduleSymbols = await extractSymbolsFromCode(resolvedPath, moduleContent)

		// 过滤出导入的符号
		if (importInfo.symbols.includes("*")) {
			// 导入所有符号
			symbols.push(...moduleSymbols)
		} else {
			// 导入特定符号
			for (const symbolName of importInfo.symbols) {
				const matchingSymbols = moduleSymbols.filter((s) => s.name === symbolName)
				symbols.push(...matchingSymbols)
			}
		}
	} catch (error) {
		console.warn("Error resolving imported symbols:", error)
	}

	return symbols
}

/**
 * 解析模块路径为绝对路径
 */
async function resolveModulePath(modulePath: string, currentFilePath: string): Promise<string | null> {
	// 相对路径解析
	if (modulePath.startsWith("./") || modulePath.startsWith("../")) {
		const currentDir = path.dirname(currentFilePath)
		const resolvedPath = path.resolve(currentDir, modulePath)

		// 尝试不同的文件扩展名
		const extensions = [".ts", ".tsx", ".js", ".jsx", ".d.ts"]
		for (const ext of extensions) {
			const pathWithExt = resolvedPath + ext
			try {
				await fs.access(pathWithExt)
				return pathWithExt
			} catch {}
		}

		// 尝试 index 文件
		for (const ext of extensions) {
			const indexPath = path.join(resolvedPath, `index${ext}`)
			try {
				await fs.access(indexPath)
				return indexPath
			} catch {}
		}
	}

	// TODO: 处理 node_modules 和绝对导入
	// 这里可以扩展支持更复杂的模块解析逻辑

	return null
}

/**
 * 从代码中提取符号定义
 */
async function extractSymbolsFromCode(filePath: string, code: string): Promise<SymbolDefinition[]> {
	const symbols: SymbolDefinition[] = []

	try {
		const ast = await getAst(filePath, code)
		if (!ast) return symbols

		// 使用 tree-sitter 查询提取符号定义
		// 这里需要根据不同语言使用不同的查询
		const symbolNodes = await findSymbolNodes(ast, filePath)

		for (const node of symbolNodes) {
			const symbol = await nodeToSymbolDefinition(node, filePath, code)
			if (symbol) {
				symbols.push(symbol)
			}
		}
	} catch (error) {
		console.warn("Error extracting symbols from code:", error)
	}

	return symbols
}

/**
 * 查找代码中的未定义符号
 */
async function findUndefinedSymbols(filePath: string, code: string): Promise<UndefinedSymbol[]> {
	const undefinedSymbols: UndefinedSymbol[] = []

	try {
		const ast = await getAst(filePath, code)
		if (!ast) return undefinedSymbols

		// 遍历 AST 查找标识符节点
		const identifierNodes = findNodesOfType(ast.rootNode, "identifier")

		for (const node of identifierNodes) {
			// 简单启发式：检查是否是函数调用或类型引用
			const parent = node.parent
			if (
				parent &&
				(parent.type === "call_expression" || parent.type === "type_identifier" || parent.type === "member_expression")
			) {
				// 检查是否在当前文件中定义
				const isDefined = await isSymbolDefinedInFile(node.text, filePath, code)
				if (!isDefined) {
					undefinedSymbols.push({
						name: node.text,
						range: new vscode.Range(
							new vscode.Position(node.startPosition.row, node.startPosition.column),
							new vscode.Position(node.endPosition.row, node.endPosition.column),
						),
						type: inferSymbolType(parent),
					})
				}
			}
		}
	} catch (error) {
		console.warn("Error finding undefined symbols:", error)
	}

	return undefinedSymbols
}

/**
 * 搜索未定义符号的定义
 */
async function searchSymbolDefinitions(undefinedSymbols: UndefinedSymbol[]): Promise<SymbolDefinition[]> {
	const symbols: SymbolDefinition[] = []

	try {
		// 获取工作区路径
		const workspaceFolders = vscode.workspace.workspaceFolders
		if (!workspaceFolders || workspaceFolders.length === 0) return symbols

		const workspacePath = workspaceFolders[0].uri.fsPath

		// 使用 ripgrep 搜索符号定义
		const rgPath = await getBinPath(vscode.env.appRoot)
		if (!rgPath) return symbols

		for (const undefinedSymbol of undefinedSymbols) {
			try {
				// 搜索函数、类、接口等定义
				const searchPatterns = [
					`function\\s+${undefinedSymbol.name}\\s*\\(`,
					`class\\s+${undefinedSymbol.name}\\s*[{<]`,
					`interface\\s+${undefinedSymbol.name}\\s*[{<]`,
					`const\\s+${undefinedSymbol.name}\\s*=`,
					`let\\s+${undefinedSymbol.name}\\s*=`,
					`var\\s+${undefinedSymbol.name}\\s*=`,
					`export\\s+.*${undefinedSymbol.name}`,
				]

				for (const pattern of searchPatterns) {
					const foundSymbols = await searchSymbolWithPattern(pattern, undefinedSymbol.name, workspacePath, rgPath)
					symbols.push(...foundSymbols.map((s) => ({ ...s, source: "undefined_symbol" as const })))
				}
			} catch (error) {
				console.warn(`Error searching for symbol ${undefinedSymbol.name}:`, error)
			}
		}
	} catch (error) {
		console.warn("Error searching symbol definitions:", error)
	}

	return symbols
}

/**
 * 通过文件路径启发式查找相关文件
 */
async function findRelatedFilesByHeuristic(currentFilePath: string): Promise<string[]> {
	const relatedFiles: string[] = []

	try {
		const workspaceFolders = vscode.workspace.workspaceFolders
		if (!workspaceFolders || workspaceFolders.length === 0) return relatedFiles

		const workspacePath = workspaceFolders[0].uri.fsPath
		const currentDir = path.dirname(currentFilePath)
		const currentBaseName = path.basename(currentFilePath, path.extname(currentFilePath))

		// 获取所有文件
		const allFiles = await executeRipgrepForFiles((await getBinPath(vscode.env.appRoot)) || "", workspacePath)

		// 启发式规则
		const heuristicRules = [
			// 同目录下的文件
			(file: string) => (path.dirname(file) === currentDir ? 0.8 : 0),
			// 相似文件名
			(file: string) => {
				const baseName = path.basename(file, path.extname(file))
				if (baseName.includes(currentBaseName) || currentBaseName.includes(baseName)) {
					return 0.7
				}
				return 0
			},
			// 测试文件关联
			(file: string) => {
				const baseName = path.basename(file, path.extname(file))
				if (
					currentBaseName.includes("test") &&
					baseName.replace(/\.test$/, "") === currentBaseName.replace(/\.test$/, "")
				) {
					return 0.9
				}
				if (baseName.includes("test") && currentBaseName === baseName.replace(/\.test$/, "")) {
					return 0.9
				}
				return 0
			},
			// 父子目录关系
			(file: string) => {
				const fileDir = path.dirname(file)
				if (fileDir.startsWith(currentDir) || currentDir.startsWith(fileDir)) {
					return 0.5
				}
				return 0
			},
		]

		// 计算每个文件的相关性分数
		const scoredFiles = allFiles
			.filter((item) => item.type === "file")
			.map((item) => ({
				path: item.path,
				score: heuristicRules.reduce((sum, rule) => sum + rule(item.path), 0),
			}))
			.filter((item) => item.score > 0)
			.sort((a, b) => b.score - a.score)
			.slice(0, 10) // 取前10个最相关的文件

		relatedFiles.push(...scoredFiles.map((item) => item.path))
	} catch (error) {
		console.warn("Error finding related files by heuristic:", error)
	}

	return relatedFiles
}

/**
 * 查找最近修改的相关文件
 */
async function findRecentlyModifiedRelated(currentFilePath: string): Promise<FileModificationInfo[]> {
	const recentFiles: FileModificationInfo[] = []

	try {
		const workspaceFolders = vscode.workspace.workspaceFolders
		if (!workspaceFolders || workspaceFolders.length === 0) return recentFiles

		const workspacePath = workspaceFolders[0].uri.fsPath
		const currentDir = path.dirname(currentFilePath)

		// 获取工作区中的所有文件
		const allFiles = await executeRipgrepForFiles((await getBinPath(vscode.env.appRoot)) || "", workspacePath)

		// 获取文件修改时间
		const now = Date.now()
		const oneWeekAgo = now - 7 * 24 * 60 * 60 * 1000 // 一周前

		for (const fileItem of allFiles.slice(0, 100)) {
			// 限制检查的文件数量
			if (fileItem.type !== "file") continue

			try {
				const stats = await fs.stat(fileItem.path)
				const lastModified = stats.mtime.getTime()

				// 只考虑最近一周修改的文件
				if (lastModified > oneWeekAgo) {
					// 计算与当前文件的相关性
					const isRelated =
						path.dirname(fileItem.path).startsWith(currentDir) || currentDir.startsWith(path.dirname(fileItem.path))

					if (isRelated) {
						recentFiles.push({
							path: fileItem.path,
							lastModified,
						})
					}
				}
			} catch (error) {
				// 忽略无法访问的文件
				continue
			}
		}

		// 按修改时间排序
		recentFiles.sort((a, b) => b.lastModified - a.lastModified)
	} catch (error) {
		console.warn("Error finding recently modified files:", error)
	}

	return recentFiles.slice(0, 5) // 返回最近修改的5个文件
}

/**
 * 从文件列表中提取符号定义
 */
async function extractSymbolsFromFiles(filePaths: string[]): Promise<SymbolDefinition[]> {
	const symbols: SymbolDefinition[] = []

	for (const filePath of filePaths) {
		try {
			const content = await fs.readFile(filePath, "utf-8")
			const fileSymbols = await extractSymbolsFromCode(filePath, content)
			symbols.push(...fileSymbols)
		} catch (error) {
			console.warn(`Error extracting symbols from file ${filePath}:`, error)
		}
	}

	return symbols
}

/**
 * 查找 AST 中的符号节点
 */
async function findSymbolNodes(ast: any, filePath: string): Promise<any[]> {
	const symbolNodes: any[] = []

	// 根据文件类型使用不同的查询策略
	const ext = path.extname(filePath).toLowerCase()

	if (ext === ".ts" || ext === ".tsx" || ext === ".js" || ext === ".jsx") {
		// TypeScript/JavaScript 符号查找
		symbolNodes.push(...findNodesOfType(ast.rootNode, "function_declaration"))
		symbolNodes.push(...findNodesOfType(ast.rootNode, "class_declaration"))
		symbolNodes.push(...findNodesOfType(ast.rootNode, "interface_declaration"))
		symbolNodes.push(...findNodesOfType(ast.rootNode, "type_alias_declaration"))
		symbolNodes.push(...findNodesOfType(ast.rootNode, "variable_declaration"))
		symbolNodes.push(...findNodesOfType(ast.rootNode, "method_definition"))
	} else if (ext === ".py") {
		// Python 符号查找
		symbolNodes.push(...findNodesOfType(ast.rootNode, "function_definition"))
		symbolNodes.push(...findNodesOfType(ast.rootNode, "class_definition"))
	} else if (ext === ".rs") {
		// Rust 符号查找
		symbolNodes.push(...findNodesOfType(ast.rootNode, "function_item"))
		symbolNodes.push(...findNodesOfType(ast.rootNode, "struct_item"))
		symbolNodes.push(...findNodesOfType(ast.rootNode, "enum_item"))
		symbolNodes.push(...findNodesOfType(ast.rootNode, "trait_item"))
	}
	// 可以继续添加其他语言的支持

	return symbolNodes
}

/**
 * 将 AST 节点转换为符号定义
 */
async function nodeToSymbolDefinition(node: any, filePath: string, code: string): Promise<SymbolDefinition | null> {
	try {
		const name = extractSymbolName(node)
		if (!name) return null

		const type = mapNodeTypeToSymbolType(node.type)
		const range = new vscode.Range(
			new vscode.Position(node.startPosition.row, node.startPosition.column),
			new vscode.Position(node.endPosition.row, node.endPosition.column),
		)

		// 提取符号内容（包括注释）
		const content = extractSymbolContent(node, code)

		return {
			name,
			type,
			filePath,
			range,
			content,
			score: 0, // 初始分数，会在合并时计算
			source: "heuristic",
		}
	} catch (error) {
		console.warn("Error converting node to symbol definition:", error)
		return null
	}
}

/**
 * 递归查找指定类型的节点
 */
function findNodesOfType(node: any, targetType: string): any[] {
	const nodes: any[] = []

	if (node.type === targetType) {
		nodes.push(node)
	}

	for (const child of node.children || []) {
		nodes.push(...findNodesOfType(child, targetType))
	}

	return nodes
}

/**
 * 检查符号是否在文件中定义
 */
async function isSymbolDefinedInFile(symbolName: string, filePath: string, code: string): Promise<boolean> {
	try {
		const ast = await getAst(filePath, code)
		if (!ast) return false

		const symbolNodes = await findSymbolNodes(ast, filePath)
		return symbolNodes.some((node) => extractSymbolName(node) === symbolName)
	} catch (error) {
		return false
	}
}

/**
 * 推断符号类型
 */
function inferSymbolType(parentNode: any): string {
	switch (parentNode.type) {
		case "call_expression":
			return "function"
		case "type_identifier":
		case "type_annotation":
			return "type"
		case "member_expression":
			return "property"
		default:
			return "variable"
	}
}

/**
 * 使用模式搜索符号
 */
async function searchSymbolWithPattern(
	pattern: string,
	symbolName: string,
	workspacePath: string,
	rgPath: string,
): Promise<SymbolDefinition[]> {
	const symbols: SymbolDefinition[] = []

	try {
		// 使用 ripgrep 搜索
		const { execFile, promisify } = isNodeEnvironment ? require("child_process") : window
		const execFileAsync = promisify(execFile)

		const args = [
			"--json",
			"--type",
			"ts",
			"--type",
			"js",
			"--type",
			"tsx",
			"--type",
			"jsx",
			"--type",
			"py",
			"--type",
			"rs",
			"-e",
			pattern,
			workspacePath,
		]

		const { stdout } = await execFileAsync(rgPath, args)
		const lines = stdout
			.trim()
			.split("\n")
			.filter((line: string) => line)

		for (const line of lines) {
			try {
				const result = JSON.parse(line)
				if (result.type === "match") {
					const filePath = result.data.path.text
					const lineNumber = result.data.line_number - 1
					const content = result.data.lines.text

					symbols.push({
						name: symbolName,
						type: inferTypeFromPattern(pattern),
						filePath,
						range: new vscode.Range(
							new vscode.Position(lineNumber, 0),
							new vscode.Position(lineNumber, content.length),
						),
						content: content.trim(),
						score: 0,
						source: "undefined_symbol",
					})
				}
			} catch (parseError) {
				// 忽略解析错误
			}
		}
	} catch (error) {
		console.warn("Error searching with pattern:", error)
	}

	return symbols
}

/**
 * 从模式推断符号类型
 */
function inferTypeFromPattern(pattern: string): SymbolDefinition["type"] {
	if (pattern.includes("function")) {
		return "function"
	}
	if (pattern.includes("class")) {
		return "class"
	}
	if (pattern.includes("interface")) {
		return "interface"
	}
	if (pattern.includes("const") || pattern.includes("let") || pattern.includes("var")) {
		return "variable"
	}
	return "variable"
}

/**
 * 提取符号名称
 */
function extractSymbolName(node: any): string | null {
	// 根据节点类型提取名称
	if (node.type === "function_declaration" || node.type === "function_definition" || node.type === "function_item") {
		const nameNode = node.children?.find((child: any) => child.type === "identifier")
		return nameNode?.text || null
	}

	if (node.type === "class_declaration" || node.type === "class_definition") {
		const nameNode = node.children?.find((child: any) => child.type === "identifier" || child.type === "type_identifier")
		return nameNode?.text || null
	}

	if (node.type === "interface_declaration" || node.type === "type_alias_declaration") {
		const nameNode = node.children?.find((child: any) => child.type === "type_identifier")
		return nameNode?.text || null
	}

	if (node.type === "variable_declaration") {
		// 查找变量声明中的标识符
		const declarator = node.children?.find((child: any) => child.type === "variable_declarator")
		if (declarator) {
			const nameNode = declarator.children?.find((child: any) => child.type === "identifier")
			return nameNode?.text || null
		}
	}

	if (node.type === "method_definition") {
		const nameNode = node.children?.find((child: any) => child.type === "property_identifier" || child.type === "identifier")
		return nameNode?.text || null
	}

	return null
}

/**
 * 映射节点类型到符号类型
 */
function mapNodeTypeToSymbolType(nodeType: string): SymbolDefinition["type"] {
	const typeMap: Record<string, SymbolDefinition["type"]> = {
		function_declaration: "function",
		function_definition: "function",
		function_item: "function",
		class_declaration: "class",
		class_definition: "class",
		interface_declaration: "interface",
		type_alias_declaration: "type",
		variable_declaration: "variable",
		method_definition: "method",
		struct_item: "type",
		enum_item: "type",
		trait_item: "interface",
	}

	return typeMap[nodeType] || "variable"
}

/**
 * 提取符号内容（包括注释）
 */
function extractSymbolContent(node: any, code: string): string {
	const lines = code.split("\n")
	const startLine = node.startPosition.row
	const endLine = node.endPosition.row

	// 尝试包含前面的注释
	let actualStartLine = startLine
	for (let i = startLine - 1; i >= 0; i--) {
		const line = lines[i].trim()
		if (line.startsWith("//") || line.startsWith("/*") || line.startsWith("*") || line === "") {
			actualStartLine = i
		} else {
			break
		}
	}

	return lines.slice(actualStartLine, endLine + 1).join("\n")
}
