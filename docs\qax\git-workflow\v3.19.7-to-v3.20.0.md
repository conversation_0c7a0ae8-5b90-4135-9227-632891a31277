# v3.19.7 到 v3.20.0 升级日志

## 基本信息
- **更新日期**: 2025-07-23
- **源版本**: v3.19.7
- **目标版本**: v3.20.0
- **变更统计**: 208 个文件变更，新增 14,248 行，删除 35,741 行

## 变更统计

### 目标版本已删除或重命名文件列表
- **删除文件** (16 个):
  - `docs/package-lock.json` - 文档依赖锁定文件
  - `eslint-rules/__tests__/no-grpc-client-object-literals.test.ts` - ESLint 规则测试
  - `eslint-rules/__tests__/no-protobuf-object-literals.test.ts` - ESLint 规则测试
  - `eslint-rules/no-grpc-client-object-literals.js` - ESLint 规则
  - `eslint-rules/no-protobuf-object-literals.js` - ESLint 规则
  - `scripts/build-proto-config.mjs` - 构建脚本
  - `scripts/generate-server-setup.mjs` - 服务器设置生成脚本
  - `src/hosts/host-providers.ts` - 主机提供商配置
  - `src/integrations/debug/DebugConsoleManager.ts` - 调试控制台管理器
  - `src/integrations/diagnostics/DiagnosticsMonitor.ts` - 诊断监控器
  - `src/standalone/ExternalDiffviewProvider.ts` - 外部差异视图提供商
  - `webview-ui/src/config.ts` - Webview 配置文件
  - `webview-ui/src/services/grpc-client-base.js` - gRPC 客户端基础文件
  - `webview-ui/src/services/grpc-client.js` - gRPC 客户端文件
  - `webview-ui/src/utils/vscode.js` - VSCode 工具文件

- **重命名文件** (4 个):
  - `src/standalone/ExternalWebviewProvider.ts` → `src/hosts/external/ExternalWebviewProvider.ts`
  - `src/standalone/grpc-types.ts` → `src/hosts/external/grpc-types.ts`
  - `src/standalone/host-bridge-client-manager.ts` → `src/hosts/external/host-bridge-client-manager.ts`
  - `src/integrations/editor/DecorationController.ts` → `src/hosts/vscode/DecorationController.ts`

### 关键文件 `package.json` 变更内容
```json
{
  "name": "claude-dev",
  "displayName": "Cline",
  "description": "Autonomous coding agent right in your IDE, capable of creating/editing files, running commands, using the browser, and more with your permission every step of the way.",
- "version": "3.19.7",
+ "version": "3.20.0",
  "scripts": {
-   "protos": "node scripts/build-proto.mjs && node scripts/generate-server-setup.mjs && node scripts/generate-host-bridge-client.mjs",
+   "protos": "node scripts/build-proto.mjs && node scripts/generate-protobus-setup.mjs && node scripts/generate-host-bridge-client.mjs",
  }
}
```

## 主要变更

### 🏗️ 架构重构和代码优化

#### 1. Host Bridge 架构重构
- **新增核心模块**: `src/hosts/host-provider.ts` - 统一的主机提供商接口
- **平台分离**: 将平台特定代码从 `standalone` 目录迁移到 `hosts/external` 目录
- **VSCode 集成**: 新增多个 VSCode 特定的 Host Bridge 处理器:
  - `src/hosts/vscode/hostbridge/diff/closeDiff.ts` - 关闭差异视图
  - `src/hosts/vscode/hostbridge/diff/getDocumentText.ts` - 获取文档文本
  - `src/hosts/vscode/hostbridge/diff/saveDocument.ts` - 保存文档
  - `src/hosts/vscode/hostbridge/diff/truncateDocument.ts` - 截断文档
  - `src/hosts/vscode/hostbridge/window/showInputBox.ts` - 显示输入框
  - `src/hosts/vscode/hostbridge/window/showSaveDialog.ts` - 显示保存对话框
  - `src/hosts/vscode/hostbridge/workspace/saveOpenDocumentIfDirty.ts` - 保存脏文档

#### 2. gRPC 通信系统升级
- **新增 ProtoBus 服务**: `src/standalone/protobus-service.ts` - 统一的协议缓冲区服务
- **生成脚本重构**:
  - 删除 `scripts/generate-server-setup.mjs`
  - 新增 `scripts/generate-protobus-setup.mjs` - ProtoBus 设置生成器
  - 新增 `scripts/proto-utils.mjs` - 协议工具函数
  - 新增 `scripts/file-utils.mjs` - 文件工具函数
- **客户端优化**: 重构 `webview-ui/src/services/grpc-client-base.ts` (251 行变更)

#### 3. 消息系统简化
- **ExtensionMessage 简化**: 移除多种消息类型，统一为 `grpc_response` 类型
- **WebviewMessage 优化**: 简化为 `grpc_request` 和 `grpc_request_cancel` 类型
- **状态管理**: 优化 `webview-ui/src/context/ExtensionStateContext.tsx` 的 gRPC 订阅机制

### 🆕 新功能添加

#### 1. 模型提供商扩展
- **Qwen 3 模型支持**: 在 Qwen 提供商中新增 Qwen 3 系列模型
- **Cerebras 模型更新**: 更新可用模型列表和上下文窗口配置
- **Hugging Face 模型描述修复**: 改进模型描述信息的准确性
- **OpenRouter 模型**: 新增 `devtral medium` 模型支持
- **XAI 模型**: 新增 6 个新模型支持

#### 2. 账户管理增强
- **多账户支持**: 改进账户视图的状态管理 (`webview-ui/src/components/account/AccountView.tsx` - 353 行变更)
- **信用余额显示**: 为所有账户显示信用余额信息
- **认证状态管理**: 优化认证状态在多窗口间的同步

#### 3. 用户界面改进
- **公告系统自动化**: 自动化主要版本发布的公告显示机制
- **启动按钮**: 新增启动按钮功能 (`35c0ced2 launch buttons`)
- **模型选择器优化**: 改进各种模型选择器的搜索和状态管理
- **设置界面**: 优化 API 配置和提供商设置界面

### 🔧 错误处理和稳定性改进

#### 1. Host Bridge 错误处理
- **超时处理**: 改进检查点操作的超时和错误处理机制
- **日志记录**: 增强 Host Bridge 错误处理和日志记录的健壮性
- **连接管理**: 优化 gRPC 连接的错误恢复机制

#### 2. 环境配置传递
- **CLINE_ENVIRONMENT**: 修复环境配置未正确传递到 webview 的问题
- **配置验证**: 改进 API 处理器构建选项的验证

#### 3. 状态管理优化
- **模型选择器**: 修复模型选择器中的过期状态问题
- **MCP 服务器**: 修复禁用时 MCP 服务器仍然启动的问题

### 🧪 测试和开发体验

#### 1. E2E 测试加速
- **测试优化**: 显著提升端到端测试的执行速度
- **Claude Code 测试**: 新增 `src/integrations/claude-code/run.test.ts` (163 行)

#### 2. 开发工具改进
- **ESLint 规则**: 增强对 VSCode API 调用的检查
- **构建脚本**: 优化协议缓冲区构建流程的清理部分

### 📦 依赖和构建系统

#### 1. 包管理优化
- **package-lock.json**: 大幅简化依赖树 (28,216 行变更)
- **文档依赖**: 移除 `docs/package-lock.json` 以简化构建
- **Mintlify 版本**: 更新文档构建工具版本

#### 2. 构建流程改进
- **环境设置**: 将环境设置移至启动和发布阶段
- **脚本优化**: 改进 `scripts/runclinecore.sh` 仅安装和运行 cline-core

## 详细文件列表

### 新增文件 (15 个)
- `scripts/file-utils.mjs` - 文件操作工具函数
- `scripts/generate-protobus-setup.mjs` - ProtoBus 设置生成器
- `scripts/proto-utils.mjs` - 协议工具函数
- `src/hosts/external/ExternalDiffviewProvider.ts` - 外部差异视图提供商
- `src/hosts/host-provider.ts` - 主机提供商接口
- `src/hosts/vscode/hostbridge/diff/closeDiff.ts` - 关闭差异视图
- `src/hosts/vscode/hostbridge/diff/getDocumentText.ts` - 获取文档文本
- `src/hosts/vscode/hostbridge/diff/saveDocument.ts` - 保存文档
- `src/hosts/vscode/hostbridge/diff/truncateDocument.ts` - 截断文档
- `src/hosts/vscode/hostbridge/window/showInputBox.ts` - 显示输入框
- `src/hosts/vscode/hostbridge/window/showSaveDialog.ts` - 显示保存对话框
- `src/hosts/vscode/hostbridge/workspace/saveOpenDocumentIfDirty.ts` - 保存脏文档
- `src/integrations/claude-code/run.test.ts` - Claude Code 运行测试
- `src/standalone/protobus-service.ts` - ProtoBus 服务

### 主要修改文件 (重点关注)
- **核心控制器**: `src/core/controller/index.ts` (419 行变更) - 主要业务逻辑重构
- **扩展入口**: `src/extension.ts` (106 行变更) - 扩展初始化和命令注册优化
- **Webview 核心**: `src/core/webview/index.ts` (14 行变更) - Webview 生命周期管理
- **状态管理**: `src/core/storage/state.ts` (410 行变更) - 状态存储和迁移逻辑
- **状态键值**: `src/core/storage/state-keys.ts` (88 行变更) - 状态键定义
- **状态迁移**: `src/core/storage/state-migrations.ts` (386 行变更) - 数据迁移逻辑
- **扩展状态上下文**: `webview-ui/src/context/ExtensionStateContext.tsx` - gRPC 订阅管理

## 升级注意事项

### ⚠️ 重要提醒

1. **架构变更**: Host Bridge 架构有重大重构，影响平台特定功能
2. **消息系统**: ExtensionMessage 和 WebviewMessage 接口有破坏性变更
3. **文件重组**: 多个核心文件被重命名或移动，影响导入路径
4. **依赖清理**: 大量依赖被清理，可能影响自定义扩展

### 🔄 兼容性说明

- **向后兼容**: 用户设置和任务历史保持兼容
- **API 变更**: 内部 API 有重大调整，第三方集成可能受影响
- **配置迁移**: 自动迁移现有配置到新的存储结构

### 📋 建议操作

1. **更新后首次启动**:
   - 验证所有 API 提供商配置正常工作
   - 检查 MCP 服务器连接状态
   - 确认账户认证状态

2. **开发者注意**:
   - 更新任何依赖内部 API 的自定义代码
   - 检查 ESLint 配置是否需要调整
   - 验证构建脚本的兼容性

3. **新功能试用**:
   - 体验新的 Qwen 3 模型
   - 测试改进的账户管理功能
   - 尝试优化后的用户界面

---

**技术支持**: 如遇到升级相关问题，请参考相关文档或提交 Issue。

**贡献者**: 感谢所有为此版本贡献代码和反馈的开发者和用户！
