/**
 * Simple test runner for hover content display
 * Run this in VSCode's debug console or terminal
 */

console.log("🧪 Starting Next Edit Hover Content Test...")

// Mock vscode module for testing
const mockVscode = {
	MarkdownString: class {
		constructor() {
			this.value = ""
			this.isTrusted = false
			this.supportHtml = false
		}

		appendMarkdown(text) {
			this.value += text
		}
	},
	window: {
		activeTextEditor: null,
		createTextEditorDecorationType: () => ({ dispose: () => {} }),
		createStatusBarItem: () => ({
			hide: () => {},
			show: () => {},
			dispose: () => {},
		}),
		onDidChangeActiveTextEditor: () => ({ dispose: () => {} }),
		onDidChangeTextEditorSelection: () => ({ dispose: () => {} }),
	},
	commands: {
		registerCommand: () => ({ dispose: () => {} }),
	},
	languages: {
		registerHoverProvider: () => ({ dispose: () => {} }),
	},
	StatusBarAlignment: { Right: 2 },
	ThemeColor: class {
		constructor(id) {
			this.id = id
		}
	},
}

// Test data
const testSuggestion = {
	id: "test_modify_1",
	type: "modify",
	category: "type_safety",
	priority: "medium",
	description: "Update function signature",
	location: {
		anchor: "function getData()",
		position: "replace",
	},
	patch: {
		oldContent: "function getData() {",
		newContent: "function getData(): Promise<Data> {",
	},
	reasoning: "Adds proper TypeScript typing for better type safety",
	filePath: "/test/file.ts",
	createdAt: new Date(),
}

// Test hover content creation
function testHoverContent() {
	console.log("📝 Testing hover content creation...")

	// Create markdown instance
	const markdown = new mockVscode.MarkdownString()
	markdown.isTrusted = true
	markdown.supportHtml = false

	// Extension icon and action buttons at the top with more spacing
	markdown.appendMarkdown(`🔧 `)
	markdown.appendMarkdown(`[Apply](command:nextEdit.applyCurrent "Apply this change")`)
	markdown.appendMarkdown(`&nbsp;&nbsp;&nbsp;&nbsp;`)
	markdown.appendMarkdown(`[Ignore](command:nextEdit.ignoreCurrent "Ignore this change")`)
	markdown.appendMarkdown(`&nbsp;&nbsp;&nbsp;&nbsp;`)
	markdown.appendMarkdown(`[Next](command:nextEdit.nextSuggestion "Next suggestion")`)
	markdown.appendMarkdown(`&nbsp;&nbsp;&nbsp;&nbsp;`)
	markdown.appendMarkdown(`[Explain](command:nextEdit.explainCurrent "Explain this change")`)

	// Show counter
	markdown.appendMarkdown(`&nbsp;&nbsp;&nbsp;&nbsp;*(1/3)*`)

	// Separator
	markdown.appendMarkdown(`\n\n---\n\n`)

	// Show suggestion description
	const description = testSuggestion.description || "Code modification"
	markdown.appendMarkdown(`**${description}**\n\n`)

	// Show the actual code diff in standard diff format
	const codeDiff = generateTestDiff(testSuggestion)
	if (codeDiff) {
		markdown.appendMarkdown(codeDiff)
	} else {
		markdown.appendMarkdown(`*No code changes to display*`)
	}

	// Show reasoning if available
	if (testSuggestion.reasoning) {
		markdown.appendMarkdown(`\n\n**Reasoning:** ${testSuggestion.reasoning}`)
	}

	console.log("📄 Generated hover content:")
	console.log(markdown.value)

	// Verify content
	const content = markdown.value
	const checks = {
		hasIcon: content.includes("🔧"),
		hasApplyButton: content.includes("[Apply]"),
		hasIgnoreButton: content.includes("[Ignore]"),
		hasNextButton: content.includes("[Next]"),
		hasExplainButton: content.includes("[Explain]"),
		hasDescription: content.includes(testSuggestion.description),
		hasDiff: content.includes("```diff"),
		hasReasoning: content.includes(testSuggestion.reasoning),
		hasCounter: content.includes("(1/3)"),
	}

	console.log("\n🔍 Content verification:")
	Object.entries(checks).forEach(([key, value]) => {
		console.log(`${value ? "✅" : "❌"} ${key}: ${value}`)
	})

	const allPresent = Object.values(checks).every((v) => v)
	console.log(`\n${allPresent ? "✅" : "❌"} Overall result: ${allPresent ? "PASS" : "FAIL"}`)

	return allPresent
}

function generateTestDiff(suggestion) {
	if (!suggestion.patch) {
		return `*No patch data available*`
	}

	switch (suggestion.type) {
		case "modify":
			let modifyResult = ""
			const oldContent = suggestion.patch.oldContent || ""
			const newContent = suggestion.patch.newContent || ""

			if (oldContent.trim()) {
				const oldLines = oldContent.split("\n")
				oldLines.forEach((line) => {
					modifyResult += `- ${line}\n`
				})
			}

			if (newContent.trim()) {
				const newLines = newContent.split("\n")
				newLines.forEach((line) => {
					modifyResult += `+ ${line}\n`
				})
			}

			return `\`\`\`diff\n${modifyResult.trim()}\n\`\`\``

		default:
			return `\`\`\`javascript\n${suggestion.patch.newContent || ""}\n\`\`\``
	}
}

// Run the test
try {
	const result = testHoverContent()
	console.log(`\n🏁 Test completed with result: ${result ? "SUCCESS" : "FAILURE"}`)
} catch (error) {
	console.error("❌ Test failed with error:", error)
}
