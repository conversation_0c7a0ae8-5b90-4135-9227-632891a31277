/**
 * Validation script for advanced NextEdit fixes
 *
 * This script validates:
 * 1. Debounce logic is correct (calls model after delay, returns immediately)
 * 2. Escape character support in anchor matching
 */

const fs = require("fs")
const path = require("path")

function validateAdvancedFixes() {
	console.log("🔧 Validating NextEdit Advanced Fixes...\n")

	const results = {
		total: 0,
		passed: 0,
		failed: 0,
		tests: [],
	}

	// Test 1: Verify debounce implementation is correct
	results.total++
	try {
		const debouncePath = path.join(__dirname, "..", "..", "utils", "createDebouncedFn.ts")
		const content = fs.readFileSync(debouncePath, "utf8")

		// Check that debounce waits for delay, then calls function and resolves immediately
		const hasCorrectFlow =
			content.includes("setTimeout(async () => {") &&
			content.includes("const result = await fn(...args)") &&
			content.includes("resolve(result)")

		// Check that cancellation is properly handled
		const hasCancellation =
			content.includes("CancellationTokenSource") &&
			content.includes("cancel()") &&
			content.includes("isCancellationRequested")

		if (hasCorrectFlow && hasCancellation) {
			results.passed++
			results.tests.push({ name: "Debounce implementation is correct", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Debounce implementation is correct", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Debounce implementation is correct", status: "ERROR", error: error.message })
	}

	// Test 2: Verify escape character support methods exist
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasEscapeSupport =
			content.includes("findAnchorInLine") &&
			content.includes("unescapeAnchorPattern") &&
			content.includes("convertAnchorToRegex") &&
			content.includes("findAttributeMatch")

		if (hasEscapeSupport) {
			results.passed++
			results.tests.push({ name: "Escape character support methods exist", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Escape character support methods exist", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Escape character support methods exist", status: "ERROR", error: error.message })
	}

	// Test 3: Verify unescapeAnchorPattern handles common escape sequences
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasEscapeSequences =
			content.includes("replace(/\\\\\"/g, '\"')") && // \"
			content.includes("replace(/\\\\'/g, \"'\")") && // \'
			content.includes("replace(/\\\\\\\\/g, '\\\\')") && // \\
			content.includes("replace(/\\\\n/g, '\\n')") && // \n
			content.includes("replace(/\\\\t/g, '\\t')") // \t

		if (hasEscapeSequences) {
			results.passed++
			results.tests.push({ name: "Common escape sequences are handled", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Common escape sequences are handled", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Common escape sequences are handled", status: "ERROR", error: error.message })
	}

	// Test 4: Verify multiple matching strategies are implemented
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasMultipleStrategies =
			content.includes("exact_literal") &&
			content.includes("unescaped_pattern") &&
			content.includes("regex_pattern") &&
			content.includes("attribute_match") &&
			content.includes("fuzzy_match")

		if (hasMultipleStrategies) {
			results.passed++
			results.tests.push({ name: "Multiple matching strategies implemented", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Multiple matching strategies implemented", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Multiple matching strategies implemented", status: "ERROR", error: error.message })
	}

	// Test 5: Verify HTML attribute matching support
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasAttributeMatching =
			content.includes("findAttributeMatch") &&
			content.includes("attrMatch = pattern.match") &&
			content.includes("attrRegex = new RegExp") &&
			content.includes("attrName") &&
			content.includes("attrValue")

		if (hasAttributeMatching) {
			results.passed++
			results.tests.push({ name: "HTML attribute matching support", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "HTML attribute matching support", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "HTML attribute matching support", status: "ERROR", error: error.message })
	}

	// Test 6: Verify fuzzy matching with quote and whitespace normalization
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasFuzzyMatching =
			content.includes("mapCleanIndexToOriginal") &&
			content.includes("replace(/[\"']/g, '')") &&
			content.includes("replace(/\\s+/g, ' ')") &&
			content.includes("fuzzy_match")

		if (hasFuzzyMatching) {
			results.passed++
			results.tests.push({ name: "Fuzzy matching with normalization", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Fuzzy matching with normalization", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Fuzzy matching with normalization", status: "ERROR", error: error.message })
	}

	// Test 7: Verify enhanced logging for debugging
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasEnhancedLogging =
			content.includes("Processing anchor pattern") &&
			content.includes("Matched using strategy") &&
			content.includes("Match position") &&
			content.includes("strategy:")

		if (hasEnhancedLogging) {
			results.passed++
			results.tests.push({ name: "Enhanced logging for debugging", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Enhanced logging for debugging", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Enhanced logging for debugging", status: "ERROR", error: error.message })
	}

	// Calculate success rate
	const successRate = (results.passed / results.total) * 100

	// Print results
	console.log("📊 Advanced Fixes Validation Results:")
	console.log("=====================================")

	results.tests.forEach((test) => {
		const icon = test.status === "PASS" ? "✅" : test.status === "FAIL" ? "❌" : "⚠️"
		console.log(`${icon} ${test.name}`)
		if (test.error) {
			console.log(`   Error: ${test.error}`)
		}
	})

	console.log("\n📈 Summary:")
	console.log(`   Total Tests: ${results.total}`)
	console.log(`   Passed: ${results.passed}`)
	console.log(`   Failed: ${results.failed}`)
	console.log(`   Success Rate: ${successRate.toFixed(1)}%`)

	const success = successRate >= 85 // 85% threshold for advanced fixes
	console.log(`\n🎯 Result: ${success ? "🎉 ADVANCED FIXES IMPLEMENTED" : "⚠️ NEEDS MORE WORK"}`)

	if (success) {
		console.log("\n✨ All advanced fixes have been successfully implemented!")
		console.log("\n🚀 Advanced Features Summary:")
		console.log("   1. ✅ Proper debounce implementation")
		console.log("      - Waits for delay before calling model")
		console.log("      - Returns immediately after model call")
		console.log("      - Handles cancellation correctly")
		console.log("")
		console.log("   2. ✅ Comprehensive escape character support")
		console.log("      - Basic escape sequences (\\\", \\', \\\\, \\n, \\t)")
		console.log("      - HTML/XML attribute matching")
		console.log("      - Regex pattern conversion")
		console.log("      - Fuzzy matching with normalization")
		console.log("      - Multiple fallback strategies")
		console.log("")
		console.log("🎯 Example Supported Patterns:")
		console.log('   • globe-container class="globe-container-xxx"')
		console.log('   • onclick="showModal(\\"confirm\\", \\"message\\")"')
		console.log('   • data-config="{\\"type\\": \\"value\\"}"')
		console.log('   • path = "C:\\\\Users\\\\<USER>\\]\\\\]/g")
		console.log("")
		console.log("📝 Testing Instructions:")
		console.log("   1. Test with escaped quotes in anchor patterns")
		console.log("   2. Test with HTML attributes containing complex values")
		console.log("   3. Verify debounce behavior during rapid typing")
		console.log("   4. Check that model calls happen after delay")
		console.log("   5. Confirm immediate return after model completion")
	} else {
		console.log("\n🔧 Some advanced fixes need additional work. Please review the failed tests.")
	}

	return success
}

// Run validation
if (require.main === module) {
	validateAdvancedFixes()
}

module.exports = { validateAdvancedFixes }
