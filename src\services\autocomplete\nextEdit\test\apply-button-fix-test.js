/**
 * Test script to verify Apply button fix
 *
 * This script validates that:
 * 1. Apply button properly stores suggestion and editor references before clearing UI
 * 2. Apply operation can proceed with stored references
 * 3. UI is cleared immediately for instant feedback
 * 4. Apply operation completes successfully
 */

const fs = require("fs")
const path = require("path")

function validateApplyButtonFix() {
	console.log("🔧 Validating NextEdit Apply Button Fix...\n")

	const results = {
		total: 0,
		passed: 0,
		failed: 0,
		tests: [],
	}

	// Test 1: Verify suggestion and editor references are stored before clearUI
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasReferenceStorage =
			content.includes("const suggestion = this.currentSuggestion") &&
			content.includes("const editor = this.currentEditor") &&
			content.includes("Store references before clearing UI")

		if (hasReferenceStorage) {
			results.passed++
			results.tests.push({ name: "Suggestion and editor references stored before clearUI", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Suggestion and editor references stored before clearUI", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({
			name: "Suggestion and editor references stored before clearUI",
			status: "ERROR",
			error: error.message,
		})
	}

	// Test 2: Verify clearUI is called after storing references
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		// Check that clearUI is called after storing references and within the if block
		const hasCorrectClearUIOrder =
			content.includes("if (suggestion && editor) {") &&
			content.includes("this.clearUI()") &&
			content.includes("Clear UI immediately to provide instant feedback")

		if (hasCorrectClearUIOrder) {
			results.passed++
			results.tests.push({ name: "clearUI called after storing references", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "clearUI called after storing references", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "clearUI called after storing references", status: "ERROR", error: error.message })
	}

	// Test 3: Verify apply operation uses stored references
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasStoredReferencesUsage =
			content.includes("await this.applySuggestionToEditor(suggestion, editor)") &&
			content.includes("filePath: suggestion.filePath") &&
			content.includes("suggestionId: suggestion.id")

		if (hasStoredReferencesUsage) {
			results.passed++
			results.tests.push({ name: "Apply operation uses stored references", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Apply operation uses stored references", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Apply operation uses stored references", status: "ERROR", error: error.message })
	}

	// Test 4: Verify error handling uses stored references
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasStoredReferencesInErrorHandling =
			content.includes("console.log(`✅ NextEdit: Applied suggestion: ${suggestion.description}`)") &&
			content.includes("Current suggestion: ${!!suggestion}") &&
			content.includes("Current editor: ${!!editor}")

		if (hasStoredReferencesInErrorHandling) {
			results.passed++
			results.tests.push({ name: "Error handling uses stored references", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Error handling uses stored references", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Error handling uses stored references", status: "ERROR", error: error.message })
	}

	// Test 5: Verify old problematic clearUI call is removed
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		// Check that clearUI is NOT called before checking suggestion and editor
		const lines = content.split("\n")
		let foundProblematicPattern = false

		for (let i = 0; i < lines.length - 5; i++) {
			const currentLine = lines[i]
			const nextFewLines = lines.slice(i + 1, i + 6).join("\n")

			// Look for clearUI followed by checking this.currentSuggestion
			if (
				currentLine.includes("this.clearUI()") &&
				nextFewLines.includes("this.currentSuggestion") &&
				nextFewLines.includes("this.currentEditor")
			) {
				foundProblematicPattern = true
				break
			}
		}

		if (!foundProblematicPattern) {
			results.passed++
			results.tests.push({ name: "Old problematic clearUI pattern removed", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Old problematic clearUI pattern removed", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Old problematic clearUI pattern removed", status: "ERROR", error: error.message })
	}

	// Test 6: Verify proper conditional structure
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasProperConditional =
			content.includes("if (suggestion && editor) {") &&
			content.includes("Both suggestion and editor available, proceeding with apply") &&
			content.includes("} else {") &&
			content.includes("Cannot apply suggestion - missing data")

		if (hasProperConditional) {
			results.passed++
			results.tests.push({ name: "Proper conditional structure", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Proper conditional structure", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Proper conditional structure", status: "ERROR", error: error.message })
	}

	// Calculate success rate
	const successRate = (results.passed / results.total) * 100

	// Print results
	console.log("📊 Apply Button Fix Validation Results:")
	console.log("======================================")

	results.tests.forEach((test) => {
		const icon = test.status === "PASS" ? "✅" : test.status === "FAIL" ? "❌" : "⚠️"
		console.log(`${icon} ${test.name}`)
		if (test.error) {
			console.log(`   Error: ${test.error}`)
		}
	})

	console.log("\n📈 Summary:")
	console.log(`   Total Tests: ${results.total}`)
	console.log(`   Passed: ${results.passed}`)
	console.log(`   Failed: ${results.failed}`)
	console.log(`   Success Rate: ${successRate.toFixed(1)}%`)

	const success = successRate >= 85 // 85% threshold
	console.log(`\n🎯 Result: ${success ? "🎉 APPLY BUTTON FIXED" : "⚠️ NEEDS MORE WORK"}`)

	if (success) {
		console.log("\n✨ Apply button fix has been successfully implemented!")
		console.log("\n🔧 Fix Details:")
		console.log("   1. ✅ Store references before clearing UI")
		console.log("      - const suggestion = this.currentSuggestion")
		console.log("      - const editor = this.currentEditor")
		console.log("      - Prevents data loss during UI clearing")
		console.log("")
		console.log("   2. ✅ Clear UI immediately for instant feedback")
		console.log("      - Called after storing references")
		console.log("      - Provides immediate visual response")
		console.log("      - User sees instant UI dismissal")
		console.log("")
		console.log("   3. ✅ Use stored references for apply operation")
		console.log("      - applySuggestionToEditor(suggestion, editor)")
		console.log("      - Event emission with suggestion data")
		console.log("      - Error handling with stored references")
		console.log("")
		console.log("🔍 Expected Log Flow (Fixed):")
		console.log("   🚀 NextEdit: handleApplySuggestion called")
		console.log("   🚀 NextEdit: Current suggestion: Update function name")
		console.log("   🚀 NextEdit: Current editor: /path/to/file.ts")
		console.log("   🚀 NextEdit: Both suggestion and editor available, proceeding with apply")
		console.log("   🚀 NextEdit: Clearing UI immediately")
		console.log("   🧹 NextEdit: Clearing UI - called from: handleApplySuggestion")
		console.log('   🔧 NextEdit: Applying suggestion xyz of type "modify"')
		console.log("   ✅ NextEdit: Applied suggestion: Update function name")
		console.log("   🔘 NextEdit: Apply command completed successfully")
		console.log("")
		console.log("❌ Previous Problematic Flow:")
		console.log("   🚀 NextEdit: Current suggestion: Update function name")
		console.log("   🚀 NextEdit: Clearing UI immediately  ← Problem: clears state")
		console.log("   ⚠️ NextEdit: Cannot apply suggestion - missing data")
		console.log("   ⚠️ NextEdit: Current suggestion: false  ← Lost due to clearUI")
		console.log("   ⚠️ NextEdit: Current editor: false     ← Lost due to clearUI")
	} else {
		console.log("\n🔧 Some apply button fixes need additional work.")
	}

	return success
}

// Run validation
if (require.main === module) {
	validateApplyButtonFix()
}

module.exports = { validateApplyButtonFix }
