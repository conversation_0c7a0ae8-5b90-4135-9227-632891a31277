import { Controller } from "../index"
import * as proto from "@/shared/proto"
import { setTodoListForTask, TodoItem, TodoStatus } from "@core/tools/QaxTodoListTool"

/**
 * Updates the complete Qax todo list for the current task
 * @param controller The controller instance
 * @param request The update request containing the new todo list
 * @returns Response with updated todo list and statistics
 */
export async function updateQaxTodoList(
	controller: Controller,
	request: proto.cline.QaxUpdateTodoListRequest,
): Promise<proto.cline.QaxTodoListResponse> {
	try {
		// Ensure there's an active task
		if (!controller.task) {
			throw new Error("No active task found")
		}

		// Convert proto QaxTodoItem to internal TodoItem format
		const todos: TodoItem[] = request.todos.map((protoTodo) => ({
			id: protoTodo.id,
			content: protoTodo.content,
			status: convertProtoStatusToInternal(protoTodo.status),
		}))

		// Update the task's todo list
		await setTodoListForTask(controller.task.taskState, todos)

		// Post updated state to webview
		await controller.postStateToWebview()

		// Calculate statistics
		const stats = calculateQaxTodoStats(todos)

		// Convert back to proto format for response
		const protoTodos = todos.map((todo) =>
			proto.cline.QaxTodoItem.create({
				id: todo.id,
				content: todo.content,
				status: convertInternalStatusToProto(todo.status),
			}),
		)

		return proto.cline.QaxTodoListResponse.create({
			todos: protoTodos,
			stats: proto.cline.QaxTodoStats.create({
				total: stats.total,
				completed: stats.completed,
				inProgress: stats.in_progress,
				pending: stats.pending,
			}),
		})
	} catch (error) {
		console.error(`Failed to update Qax todo list: ${error}`)
		throw error
	}
}

/**
 * Convert proto QaxTodoStatus to internal TodoStatus
 */
function convertProtoStatusToInternal(protoStatus: proto.cline.QaxTodoStatus): TodoStatus {
	switch (protoStatus) {
		case proto.cline.QaxTodoStatus.QAX_TODO_STATUS_COMPLETED:
			return "completed"
		case proto.cline.QaxTodoStatus.QAX_TODO_STATUS_IN_PROGRESS:
			return "in_progress"
		case proto.cline.QaxTodoStatus.QAX_TODO_STATUS_PENDING:
		default:
			return "pending"
	}
}

/**
 * Convert internal TodoStatus to proto QaxTodoStatus
 */
function convertInternalStatusToProto(status: TodoStatus): proto.cline.QaxTodoStatus {
	switch (status) {
		case "completed":
			return proto.cline.QaxTodoStatus.QAX_TODO_STATUS_COMPLETED
		case "in_progress":
			return proto.cline.QaxTodoStatus.QAX_TODO_STATUS_IN_PROGRESS
		case "pending":
		default:
			return proto.cline.QaxTodoStatus.QAX_TODO_STATUS_PENDING
	}
}

/**
 * Calculate todo list statistics
 */
function calculateQaxTodoStats(todos: TodoItem[]) {
	const total = todos.length
	const completed = todos.filter((t) => t.status === "completed").length
	const in_progress = todos.filter((t) => t.status === "in_progress").length
	const pending = todos.filter((t) => t.status === "pending").length

	return { total, completed, in_progress, pending }
}
