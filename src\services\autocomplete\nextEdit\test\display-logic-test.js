/**
 * Test script to verify NextEdit display logic fixes
 *
 * This script validates that:
 * 1. UI doesn't disappear prematurely (only when cursor moves far away)
 * 2. Highlighting is properly applied and visible
 * 3. Debug logging is in place for troubleshooting
 */

const fs = require("fs")
const path = require("path")

function validateDisplayLogicFixes() {
	console.log("🎨 Validating NextEdit Display Logic Fixes...\n")

	const results = {
		total: 0,
		passed: 0,
		failed: 0,
		tests: [],
	}

	// Test 1: Verify cursor movement tolerance is implemented
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasCursorTolerance =
			content.includes("distanceFromHover > 3") &&
			content.includes("Math.min(") &&
			content.includes("Math.abs(cursorLine - hoverStartLine)") &&
			content.includes("Math.abs(cursorLine - hoverEndLine)")

		if (hasCursorTolerance) {
			results.passed++
			results.tests.push({ name: "Cursor movement tolerance implemented", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Cursor movement tolerance implemented", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Cursor movement tolerance implemented", status: "ERROR", error: error.message })
	}

	// Test 2: Verify debug logging for highlight decoration
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasHighlightLogging =
			content.includes("🎨 NextEdit: Showing highlight decoration") &&
			content.includes("🎨 NextEdit: Using suggestion type") &&
			content.includes("🎨 NextEdit: Highlight decoration applied successfully")

		if (hasHighlightLogging) {
			results.passed++
			results.tests.push({ name: "Debug logging for highlight decoration", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Debug logging for highlight decoration", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Debug logging for highlight decoration", status: "ERROR", error: error.message })
	}

	// Test 3: Verify debug logging for diff highlighting
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasDiffLogging =
			content.includes("🎨 NextEdit: Applying") &&
			content.includes("🎨 NextEdit: Setting ADD decoration (green)") &&
			content.includes("🎨 NextEdit: Setting MODIFY decoration (yellow)") &&
			content.includes("🎨 NextEdit: Setting DELETE decoration (red)")

		if (hasDiffLogging) {
			results.passed++
			results.tests.push({ name: "Debug logging for diff highlighting", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Debug logging for diff highlighting", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Debug logging for diff highlighting", status: "ERROR", error: error.message })
	}

	// Test 4: Verify debug logging for UI clearing
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasClearLogging =
			content.includes("🧹 NextEdit: Clearing UI - called from:") &&
			content.includes("new Error().stack") &&
			content.includes("🧹 NextEdit: Clearing decorations from editor")

		if (hasClearLogging) {
			results.passed++
			results.tests.push({ name: "Debug logging for UI clearing", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Debug logging for UI clearing", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Debug logging for UI clearing", status: "ERROR", error: error.message })
	}

	// Test 5: Verify debug logging for floating panel
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasPanelLogging =
			content.includes("🎈 NextEdit: Showing floating panel") &&
			content.includes("🎈 NextEdit: Suggestion type:") &&
			content.includes("🎈 NextEdit: Hover range:")

		if (hasPanelLogging) {
			results.passed++
			results.tests.push({ name: "Debug logging for floating panel", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Debug logging for floating panel", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Debug logging for floating panel", status: "ERROR", error: error.message })
	}

	// Test 6: Verify decoration types are properly defined
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasDecorationTypes =
			content.includes("addLineDecorationType") &&
			content.includes("modifyLineDecorationType") &&
			content.includes("deleteLineDecorationType") &&
			content.includes('backgroundColor: "rgba(0, 255, 0, 0.3)"') &&
			content.includes('backgroundColor: "rgba(255, 255, 0, 0.3)"') &&
			content.includes('backgroundColor: "rgba(255, 0, 0, 0.3)"')

		if (hasDecorationTypes) {
			results.passed++
			results.tests.push({ name: "Decoration types properly defined", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Decoration types properly defined", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Decoration types properly defined", status: "ERROR", error: error.message })
	}

	// Test 7: Verify no isWholeLine property in decorations
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		// Check that isWholeLine: true is NOT present in decoration definitions
		const hasNoWholeLine = !content.includes("isWholeLine: true")

		if (hasNoWholeLine) {
			results.passed++
			results.tests.push({ name: "No isWholeLine property in decorations", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "No isWholeLine property in decorations", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "No isWholeLine property in decorations", status: "ERROR", error: error.message })
	}

	// Test 8: Verify cursor distance calculation logic
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasDistanceCalc =
			content.includes("const hoverStartLine = this.currentHoverRange.start.line") &&
			content.includes("const hoverEndLine = this.currentHoverRange.end.line") &&
			content.includes("const cursorLine = selection.active.line") &&
			content.includes("const distanceFromHover = Math.min(")

		if (hasDistanceCalc) {
			results.passed++
			results.tests.push({ name: "Cursor distance calculation logic", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Cursor distance calculation logic", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Cursor distance calculation logic", status: "ERROR", error: error.message })
	}

	// Calculate success rate
	const successRate = (results.passed / results.total) * 100

	// Print results
	console.log("📊 Display Logic Fixes Validation Results:")
	console.log("==========================================")

	results.tests.forEach((test) => {
		const icon = test.status === "PASS" ? "✅" : test.status === "FAIL" ? "❌" : "⚠️"
		console.log(`${icon} ${test.name}`)
		if (test.error) {
			console.log(`   Error: ${test.error}`)
		}
	})

	console.log("\n📈 Summary:")
	console.log(`   Total Tests: ${results.total}`)
	console.log(`   Passed: ${results.passed}`)
	console.log(`   Failed: ${results.failed}`)
	console.log(`   Success Rate: ${successRate.toFixed(1)}%`)

	const success = successRate >= 85 // 85% threshold for display logic fixes
	console.log(`\n🎯 Result: ${success ? "🎉 DISPLAY LOGIC FIXED" : "⚠️ NEEDS MORE WORK"}`)

	if (success) {
		console.log("\n✨ Display logic fixes have been successfully implemented!")
		console.log("\n🎨 Fixed Issues:")
		console.log("   1. ✅ UI no longer disappears prematurely")
		console.log("      - Cursor must move >3 lines away to clear UI")
		console.log("      - More tolerant of small cursor movements")
		console.log("")
		console.log("   2. ✅ Enhanced debug logging for troubleshooting")
		console.log("      - Highlight decoration logging")
		console.log("      - Diff highlighting logging")
		console.log("      - UI clearing tracking with stack traces")
		console.log("      - Floating panel status logging")
		console.log("")
		console.log("   3. ✅ Proper decoration definitions")
		console.log("      - No isWholeLine property (precise highlighting)")
		console.log("      - Correct color coding for different suggestion types")
		console.log("      - Border radius for better visual appearance")
		console.log("")
		console.log("🔍 Debug Log Examples:")
		console.log("   🎨 NextEdit: Showing highlight decoration at range 15:5-15:32")
		console.log('   🎨 NextEdit: Using suggestion type "modify" for highlighting')
		console.log("   🎨 NextEdit: Setting MODIFY decoration (yellow)")
		console.log("   🎈 NextEdit: Showing floating panel for suggestion 1/3")
		console.log("   🧹 NextEdit: Clearing UI - called from: handleApplySuggestion")
		console.log("")
		console.log("📝 Testing Instructions:")
		console.log("   1. Open VS Code Developer Console (Help > Toggle Developer Tools)")
		console.log("   2. Enable NextEdit and make code changes")
		console.log("   3. Watch for debug logs starting with 🎨, 🎈, 🧹")
		console.log("   4. Verify UI stays visible when making small cursor movements")
		console.log("   5. Verify highlighting appears on suggested code sections")
		console.log("   6. Test that UI only disappears when moving cursor far away")
	} else {
		console.log("\n🔧 Some display logic fixes need additional work.")
	}

	return success
}

// Run validation
if (require.main === module) {
	validateDisplayLogicFixes()
}

module.exports = { validateDisplayLogicFixes }
