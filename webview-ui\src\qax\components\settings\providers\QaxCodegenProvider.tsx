import { openAiModelInfoSaneDefaults } from "@shared/api"
import { getAsVar, VSC_DESCRIPTION_FOREGROUND } from "@/utils/vscStyles"
import { VSCodeCheckbox } from "@vscode/webview-ui-toolkit/react"
import { useState } from "react"
import { DebouncedTextField } from "@/components/settings/common/DebouncedTextField"
import { ModelInfoView } from "@/components/settings/common/ModelInfoView"
import { normalizeApiConfiguration } from "@/components/settings/utils/providerUtils"
import { useExtensionState } from "@/context/ExtensionStateContext"
import { useApiConfigurationHandlers } from "@/components/settings/utils/useApiConfigurationHandlers"
import { ClineAccountInfoCard } from "@/components/settings/ClineAccountInfoCard"
import QaxCodegenModelPicker from "../QaxCodegenModelPicker"
import { Mode } from "@shared/ChatSettings"

/**
 * QAX Codegen 提供器配置组件属性
 * <AUTHOR>
 */
interface QaxCodegenProviderProps {
	showModelOptions: boolean
	isPopup?: boolean
	currentMode: Mode
}

/**
 * QAX Codegen 提供器配置组件
 * 提供 QAX Codegen 服务的完整配置界面，包括账户信息、模型选择和参数配置
 * <AUTHOR>
 */
export const QaxCodegenProvider = ({ showModelOptions, isPopup, currentMode }: QaxCodegenProviderProps) => {
	const { apiConfiguration } = useExtensionState()
	const { handleFieldChange } = useApiConfigurationHandlers()
	const [modelConfigurationSelected, setModelConfigurationSelected] = useState(false)
	const { selectedModelId, selectedModelInfo } = normalizeApiConfiguration(apiConfiguration, currentMode)

	// 根据当前模式获取对应的模型信息
	const getCurrentModelInfo = () => {
		return currentMode === "plan"
			? apiConfiguration?.planModeQaxCodegenModelInfo
			: apiConfiguration?.actModeQaxCodegenModelInfo
	}

	// 根据当前模式更新模型信息
	const updateModelInfo = (modelInfo: any) => {
		if (currentMode === "plan") {
			handleFieldChange("planModeQaxCodegenModelInfo", modelInfo)
		} else {
			handleFieldChange("actModeQaxCodegenModelInfo", modelInfo)
		}
	}

	return (
		<div>
			<ClineAccountInfoCard />

			<div style={{ marginTop: 15 }}>
				<QaxCodegenModelPicker isPopup={isPopup} currentMode={currentMode} />
			</div>

			<div
				style={{
					color: getAsVar(VSC_DESCRIPTION_FOREGROUND),
					display: "flex",
					margin: "15px 0 10px 0",
					cursor: "pointer",
					alignItems: "center",
				}}
				onClick={() => setModelConfigurationSelected((val) => !val)}>
				<span
					className={`codicon ${modelConfigurationSelected ? "codicon-chevron-down" : "codicon-chevron-right"}`}
					style={{ marginRight: "4px" }}
				/>
				<span style={{ fontWeight: 500, textTransform: "uppercase" }}>模型配置</span>
			</div>

			{modelConfigurationSelected && (
				<>
					<VSCodeCheckbox
						checked={!!getCurrentModelInfo()?.supportsImages}
						onChange={(e: any) => {
							const isChecked = e.target.checked === true
							const modelInfo = getCurrentModelInfo() || { ...openAiModelInfoSaneDefaults }
							updateModelInfo({ ...modelInfo, supportsImages: isChecked })
						}}>
						支持图像
					</VSCodeCheckbox>

					<VSCodeCheckbox
						checked={!!getCurrentModelInfo()?.isR1FormatRequired}
						onChange={(e: any) => {
							const isChecked = e.target.checked === true
							const modelInfo = getCurrentModelInfo() || { ...openAiModelInfoSaneDefaults }
							updateModelInfo({ ...modelInfo, isR1FormatRequired: isChecked })
						}}>
						启用 R1 消息格式
					</VSCodeCheckbox>

					<div style={{ display: "flex", gap: 10, marginTop: "5px" }}>
						<DebouncedTextField
							initialValue={
								getCurrentModelInfo()?.contextWindow?.toString() ||
								openAiModelInfoSaneDefaults.contextWindow?.toString() ||
								""
							}
							style={{ flex: 1 }}
							onChange={(value) => {
								const modelInfo = getCurrentModelInfo() || { ...openAiModelInfoSaneDefaults }
								updateModelInfo({ ...modelInfo, contextWindow: Number(value) })
							}}>
							<span style={{ fontWeight: 500 }}>上下文窗口大小</span>
						</DebouncedTextField>

						<DebouncedTextField
							initialValue={
								getCurrentModelInfo()?.maxTokens?.toString() ||
								openAiModelInfoSaneDefaults.maxTokens?.toString() ||
								""
							}
							style={{ flex: 1 }}
							onChange={(value) => {
								const modelInfo = getCurrentModelInfo() || { ...openAiModelInfoSaneDefaults }
								updateModelInfo({ ...modelInfo, maxTokens: Number(value) })
							}}>
							<span style={{ fontWeight: 500 }}>最大输出令牌数</span>
						</DebouncedTextField>
					</div>

					<div style={{ display: "flex", gap: 10, marginTop: "5px" }}>
						<DebouncedTextField
							initialValue={
								getCurrentModelInfo()?.inputPrice?.toString() ||
								openAiModelInfoSaneDefaults.inputPrice?.toString() ||
								""
							}
							style={{ flex: 1 }}
							onChange={(value) => {
								const modelInfo = getCurrentModelInfo() || { ...openAiModelInfoSaneDefaults }
								updateModelInfo({ ...modelInfo, inputPrice: Number(value) })
							}}>
							<span style={{ fontWeight: 500 }}>输入价格 / 100万令牌</span>
						</DebouncedTextField>

						<DebouncedTextField
							initialValue={
								getCurrentModelInfo()?.outputPrice?.toString() ||
								openAiModelInfoSaneDefaults.outputPrice?.toString() ||
								""
							}
							style={{ flex: 1 }}
							onChange={(value) => {
								const modelInfo = getCurrentModelInfo() || { ...openAiModelInfoSaneDefaults }
								updateModelInfo({ ...modelInfo, outputPrice: Number(value) })
							}}>
							<span style={{ fontWeight: 500 }}>输出价格 / 100万令牌</span>
						</DebouncedTextField>
					</div>

					<div style={{ display: "flex", gap: 10, marginTop: "5px" }}>
						<DebouncedTextField
							initialValue={
								getCurrentModelInfo()?.temperature?.toString() ||
								openAiModelInfoSaneDefaults.temperature?.toString() ||
								""
							}
							onChange={(value) => {
								const modelInfo = getCurrentModelInfo() || { ...openAiModelInfoSaneDefaults }

								const shouldPreserveFormat = value.endsWith(".") || (value.includes(".") && value.endsWith("0"))

								const temperature =
									value === ""
										? openAiModelInfoSaneDefaults.temperature
										: shouldPreserveFormat
											? (value as any)
											: parseFloat(value)

								updateModelInfo({ ...modelInfo, temperature })
							}}>
							<span style={{ fontWeight: 500 }}>Temperature</span>
						</DebouncedTextField>
					</div>
				</>
			)}

			<p
				style={{
					fontSize: "12px",
					marginTop: 10,
					color: "var(--vscode-descriptionForeground)",
				}}>
				QAX Codegen 使用 JWT 认证，请确保已登录 QAX 账户。
			</p>

			{showModelOptions && (
				<ModelInfoView selectedModelId={selectedModelId} modelInfo={selectedModelInfo} isPopup={isPopup} />
			)}
		</div>
	)
}
