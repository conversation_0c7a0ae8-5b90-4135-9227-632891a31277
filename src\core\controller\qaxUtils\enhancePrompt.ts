import { Controller } from "../index"
import { StringRequest } from "../../../shared/proto/common"
import { getAllExtensionState } from "../../storage/state"
import { buildApiHandler } from "../../../api"
import { ApiConfiguration } from "../../../shared/api"

/**
 * Enhances a given prompt using an API provider
 * @param controller The controller instance
 * @param request The request containing the prompt to enhance
 * @returns Object with the enhanced prompt
 */
export async function enhancePrompt(controller: Controller, request: StringRequest): Promise<{ value: string }> {
	const prompt = request.value || ""

	try {
		const { apiConfiguration } = await getAllExtensionState(controller.context)
		const currentMode = await controller.getCurrentMode()
		const apiHandler = buildApiHandler(apiConfiguration as ApiConfiguration, currentMode)

		const systemPrompt =
			"You are a helpful assistant that enhances and improves user prompts to make them clearer, more specific, and more effective."
		const messages = [
			{
				role: "user" as const,
				content: [
					{
						type: "text" as const,
						text: `Please enhance this prompt to make it clearer and more effective:\n\n${prompt}`,
					},
				],
			},
		]

		const stream = apiHandler.createMessage(systemPrompt, messages)
		let enhancedPrompt = ""

		for await (const chunk of stream) {
			if (chunk.type === "text") {
				enhancedPrompt += chunk.text
			}
		}

		return {
			value: enhancedPrompt.trim() || prompt, // Return original if enhancement fails
		}
	} catch (error) {
		console.error("Error enhancing prompt:", error)
		return {
			value: prompt, // Return original prompt if enhancement fails
		}
	}
}
