/**
 * Test script to verify anchor pattern filtering and improved prompt
 *
 * This script validates that:
 * 1. Suggestions with missing anchor patterns are discarded
 * 2. Suggestions with too short anchor patterns are discarded
 * 3. Improved prompt guidelines for more precise anchor patterns
 */

const fs = require("fs")
const path = require("path")

function validateAnchorFiltering() {
	console.log("🔍 Validating NextEdit Anchor Pattern Filtering...\n")

	const results = {
		total: 0,
		passed: 0,
		failed: 0,
		tests: [],
	}

	// Test 1: Verify filterValidSuggestions method exists
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasFilterMethod =
			content.includes("filterValidSuggestions") &&
			content.includes("private async filterValidSuggestions") &&
			content.includes("NextEditSuggestion[]")

		if (hasFilterMethod) {
			results.passed++
			results.tests.push({ name: "filterValidSuggestions method exists", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "filterValidSuggestions method exists", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "filterValidSuggestions method exists", status: "ERROR", error: error.message })
	}

	// Test 2: Verify anchor length validation (minimum 8 characters)
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasLengthCheck = content.includes("anchorPattern.length < 8") && content.includes("anchor too short")

		if (hasLengthCheck) {
			results.passed++
			results.tests.push({ name: "Anchor length validation (min 8 chars)", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Anchor length validation (min 8 chars)", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Anchor length validation (min 8 chars)", status: "ERROR", error: error.message })
	}

	// Test 3: Verify showSuggestions calls filterValidSuggestions
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasFilterCall =
			content.includes("await this.filterValidSuggestions(suggestions)") &&
			content.includes("validSuggestions.length") &&
			content.includes("after filtering")

		if (hasFilterCall) {
			results.passed++
			results.tests.push({ name: "showSuggestions calls filterValidSuggestions", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "showSuggestions calls filterValidSuggestions", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "showSuggestions calls filterValidSuggestions", status: "ERROR", error: error.message })
	}

	// Test 4: Verify anchor not found logging
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasNotFoundLogging =
			content.includes("anchor not found") &&
			content.includes("Discarding suggestion") &&
			content.includes("anchor too short")

		if (hasNotFoundLogging) {
			results.passed++
			results.tests.push({ name: "Anchor not found logging", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Anchor not found logging", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Anchor not found logging", status: "ERROR", error: error.message })
	}

	// Test 5: Verify fallback cursor position removal
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		// Should NOT have fallback cursor position logic
		const hasNoFallback =
			!content.includes("Using cursor position as fallback") &&
			content.includes("Suggestion will be discarded due to missing anchor")

		if (hasNoFallback) {
			results.passed++
			results.tests.push({ name: "Fallback cursor position removed", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Fallback cursor position removed", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Fallback cursor position removed", status: "ERROR", error: error.message })
	}

	// Test 6: Verify improved anchor guidelines in prompt
	results.total++
	try {
		const enginePath = path.join(__dirname, "..", "NextEditRecommendationEngine.ts")
		const content = fs.readFileSync(enginePath, "utf8")

		const hasImprovedGuidelines =
			content.includes("at least 8-15 characters long") &&
			content.includes("function name and part of parameters") &&
			content.includes("class name and method") &&
			content.includes("uniquely identify the location")

		if (hasImprovedGuidelines) {
			results.passed++
			results.tests.push({ name: "Improved anchor guidelines in prompt", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Improved anchor guidelines in prompt", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Improved anchor guidelines in prompt", status: "ERROR", error: error.message })
	}

	// Test 7: Verify specific anchor pattern examples
	results.total++
	try {
		const enginePath = path.join(__dirname, "..", "NextEditRecommendationEngine.ts")
		const content = fs.readFileSync(enginePath, "utf8")

		const hasSpecificExamples =
			content.includes("function calculateTotal(items, tax)") &&
			content.includes("class UserService { validateEmail") &&
			content.includes("const apiEndpoint = 'https://api") &&
			content.includes('div class="container" id="main"')

		if (hasSpecificExamples) {
			results.passed++
			results.tests.push({ name: "Specific anchor pattern examples", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Specific anchor pattern examples", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Specific anchor pattern examples", status: "ERROR", error: error.message })
	}

	// Test 8: Verify generic pattern warnings
	results.total++
	try {
		const enginePath = path.join(__dirname, "..", "NextEditRecommendationEngine.ts")
		const content = fs.readFileSync(enginePath, "utf8")

		const hasGenericWarnings =
			content.includes("Avoid overly generic patterns") &&
			content.includes('"return", "if", "const"') &&
			content.includes("appear only once")

		if (hasGenericWarnings) {
			results.passed++
			results.tests.push({ name: "Generic pattern warnings", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Generic pattern warnings", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Generic pattern warnings", status: "ERROR", error: error.message })
	}

	// Calculate success rate
	const successRate = (results.passed / results.total) * 100

	// Print results
	console.log("📊 Anchor Pattern Filtering Validation Results:")
	console.log("===============================================")

	results.tests.forEach((test) => {
		const icon = test.status === "PASS" ? "✅" : test.status === "FAIL" ? "❌" : "⚠️"
		console.log(`${icon} ${test.name}`)
		if (test.error) {
			console.log(`   Error: ${test.error}`)
		}
	})

	console.log("\n📈 Summary:")
	console.log(`   Total Tests: ${results.total}`)
	console.log(`   Passed: ${results.passed}`)
	console.log(`   Failed: ${results.failed}`)
	console.log(`   Success Rate: ${successRate.toFixed(1)}%`)

	const success = successRate >= 85 // 85% threshold for anchor filtering
	console.log(`\n🎯 Result: ${success ? "🎉 ANCHOR FILTERING IMPLEMENTED" : "⚠️ NEEDS MORE WORK"}`)

	if (success) {
		console.log("\n✨ Anchor pattern filtering has been successfully implemented!")
		console.log("\n🔍 Filtering Features:")
		console.log("   1. ✅ Suggestions with missing anchors are discarded")
		console.log("   2. ✅ Suggestions with short anchors (<8 chars) are discarded")
		console.log("   3. ✅ Suggestions with unfindable anchors are discarded")
		console.log("   4. ✅ Comprehensive logging for debugging")
		console.log("")
		console.log("📝 Improved Anchor Guidelines:")
		console.log("   • Minimum 8-15 characters long")
		console.log("   • Include function names with parameters")
		console.log("   • Include class names with methods")
		console.log("   • Include variable names with context")
		console.log("   • Include HTML tags with attributes")
		console.log('   • Avoid generic patterns like "return", "if", "const"')
		console.log("   • Ensure patterns appear only once in target location")
		console.log("")
		console.log("🎯 Example Good Anchor Patterns:")
		console.log('   ✅ "function calculateTotal(items, tax)"')
		console.log('   ✅ "class UserService { validateEmail"')
		console.log('   ✅ "const apiEndpoint = \'https://api"')
		console.log('   ✅ "div class=\\"container\\" id=\\"main\\""')
		console.log("")
		console.log("❌ Example Bad Anchor Patterns:")
		console.log('   ❌ "return" (too generic)')
		console.log('   ❌ "if" (too short and generic)')
		console.log('   ❌ "const x" (too short)')
		console.log('   ❌ "function" (too generic)')
		console.log("")
		console.log("🔍 Debug Log Examples:")
		console.log("   📋 NextEdit: Received 3 suggestions, filtering valid ones...")
		console.log('   ⚠️ NextEdit: Discarding suggestion xyz - anchor too short: "if"')
		console.log('   ❌ NextEdit: Discarding suggestion abc - anchor not found: "nonexistent"')
		console.log("   ✅ NextEdit: Valid suggestion def - anchor found")
		console.log("   📋 NextEdit: 1 valid suggestions after filtering")
	} else {
		console.log("\n🔧 Some anchor filtering features need additional work.")
	}

	return success
}

// Run validation
if (require.main === module) {
	validateAnchorFiltering()
}

module.exports = { validateAnchorFiltering }
