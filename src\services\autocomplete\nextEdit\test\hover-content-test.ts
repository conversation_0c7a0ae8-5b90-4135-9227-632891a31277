import * as vscode from "vscode"
import { NextEditUIProvider } from "../NextEditUIProvider"
import { NextEditSuggestion, NextEditCategory, NextEditType } from "../types/NextEditTypes"

/**
 * Test program to verify hover content display
 */
export async function testHoverContentDisplay(): Promise<void> {
	console.log("🧪 Starting Hover Content Display Test...")

	// Create test suggestions
	const testSuggestions: NextEditSuggestion[] = [
		{
			id: "test_add_1",
			type: NextEditType.ADD,
			description: "Change: none ➜ error handling",
			location: {
				anchor: "function processUser(user)",
				position: "after",
			},
			patch: {
				oldContent: "",
				newContent: "if (!user) {\n  throw new Error('User is required');\n}",
			},
			filePath: "/test/file.js",
			createdAt: new Date(),
		},
		{
			id: "test_modify_1",
			type: NextEditType.MODIFY,
			description: "Change: addEvent ➜ addNewEvent",
			location: {
				anchor: "onClick={addEvent}",
				position: "replace",
			},
			patch: {
				oldContent: "onClick={addEvent}",
				newContent: "onClick={addNewEvent}",
			},
			filePath: "/test/file.tsx",
			createdAt: new Date(),
		},
		{
			id: "test_delete_1",
			type: NextEditType.DELETE,
			description: "Change: unusedVar ➜ none",
			location: {
				anchor: "const unusedVar = 'test';",
				position: "replace",
			},
			patch: {
				oldContent: "const unusedVar = 'test';",
				newContent: "",
			},
			filePath: "/test/file.js",
			createdAt: new Date(),
		},
	]

	// Create UI provider instance
	const uiProvider = new NextEditUIProvider()

	// Test hover content generation for each suggestion type
	console.log("🔍 Testing hover content generation...")

	for (let i = 0; i < testSuggestions.length; i++) {
		const suggestion = testSuggestions[i]
		console.log(`\n📝 Testing suggestion ${i + 1}: ${suggestion.description}`)
		console.log(`   Type: ${suggestion.type}`)
		console.log(`   File: ${suggestion.filePath}`)

		try {
			// Access the private method using type assertion
			const hoverContent = (uiProvider as any).createHoverContent(suggestion, i, testSuggestions.length)

			if (hoverContent && hoverContent.value) {
				console.log(`✅ Hover content generated successfully`)
				console.log(`📄 Content preview:`)
				console.log(`${hoverContent.value.substring(0, 200)}...`)

				// Check if content contains expected elements
				const content = hoverContent.value
				const hasIcon = content.includes("🔧")
				const hasApplyButton = content.includes("[Apply]")
				const hasIgnoreButton = content.includes("[Ignore]")
				const hasNextButton = content.includes("[Next]")
				const hasExplainButton = content.includes("[Explain]")
				const hasDescription = content.includes(suggestion.description)
				const hasDiff = content.includes("```diff") || content.includes("```")
				const hasColoredBackground = content.includes("background-color:")
				const hasLineNumbers = /\d{3}\s+\d{3}/.test(content) || /\d{3}\s+\-/.test(content) || /\d{3}\s+\+/.test(content)

				console.log(`🔧 Has extension icon: ${hasIcon}`)
				console.log(`🔘 Has Apply button: ${hasApplyButton}`)
				console.log(`🔘 Has Ignore button: ${hasIgnoreButton}`)
				console.log(`🔘 Has Next button: ${hasNextButton}`)
				console.log(`🔘 Has Explain button: ${hasExplainButton}`)
				console.log(`📝 Has description: ${hasDescription}`)
				console.log(`📊 Has diff content: ${hasDiff}`)
				console.log(`🎨 Has colored background: ${hasColoredBackground}`)
				console.log(`🔢 Has line numbers: ${hasLineNumbers}`)

				const allElementsPresent =
					hasIcon && hasApplyButton && hasIgnoreButton && hasNextButton && hasExplainButton && hasDescription && hasDiff

				if (allElementsPresent) {
					console.log(`✅ All expected elements present in hover content`)
				} else {
					console.log(`❌ Some elements missing from hover content`)
				}
			} else {
				console.log(`❌ Failed to generate hover content`)
			}
		} catch (error) {
			console.error(`❌ Error testing suggestion ${i + 1}:`, error)
		}
	}

	// Test diff generation specifically
	console.log(`\n🔍 Testing diff generation methods...`)

	for (const suggestion of testSuggestions) {
		try {
			// Test new line number diff method
			const lineNumberDiff = (uiProvider as any).generateLineNumberDiff(suggestion)
			console.log(`\n📊 Line number diff for ${suggestion.type} operation:`)
			console.log(lineNumberDiff)

			// Test context and color features
			const hasLineNumbers =
				/\d{3}\s+\d{3}/.test(lineNumberDiff) || /\d{3}\s+\-/.test(lineNumberDiff) || /\d{3}\s+\+/.test(lineNumberDiff)
			const hasColoredBackground = lineNumberDiff.includes("background-color:")
			const hasRedBackground = lineNumberDiff.includes("background-color: #5a2d2d")
			const hasGreenBackground = lineNumberDiff.includes("background-color: #2d5a2d")

			console.log(`🔢 Has line numbers: ${hasLineNumbers}`)
			console.log(`🎨 Has colored backgrounds: ${hasColoredBackground}`)

			// Verify diff format based on type
			if (suggestion.type === NextEditType.ADD) {
				if (hasGreenBackground && hasLineNumbers) {
					console.log(`✅ Add diff format correct (green background + line numbers)`)
				} else {
					console.log(`❌ Add diff format incorrect`)
				}
			} else if (suggestion.type === NextEditType.MODIFY) {
				if (hasRedBackground && hasGreenBackground && hasLineNumbers) {
					console.log(`✅ Modify diff format correct (red + green backgrounds + line numbers)`)
				} else {
					console.log(`❌ Modify diff format incorrect`)
				}
			} else if (suggestion.type === NextEditType.DELETE) {
				if (hasRedBackground && hasLineNumbers) {
					console.log(`✅ Delete diff format correct (red background + line numbers)`)
				} else {
					console.log(`❌ Delete diff format incorrect`)
				}
			}

			// Test standard diff as well
			const standardDiff = (uiProvider as any).generateStandardDiff(suggestion)
			console.log(`\n📊 Standard diff for ${suggestion.type} operation:`)
			console.log(standardDiff)
		} catch (error) {
			console.error(`❌ Error generating diff for ${suggestion.type}:`, error)
		}
	}

	// Clean up
	uiProvider.dispose()

	console.log(`\n🏁 Hover Content Display Test completed`)
}

/**
 * Run the test when this file is executed
 */
if (require.main === module) {
	testHoverContentDisplay().catch(console.error)
}
