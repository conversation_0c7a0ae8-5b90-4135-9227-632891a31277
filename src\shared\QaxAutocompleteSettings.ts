/**
 * Qax Autocomplete provider types
 */
export type QaxAutocompleteProvider = "openai" | "fim"

/**
 * Qax Autocomplete configuration settings for QAX Complete
 */
export interface QaxAutocompleteSettings {
	/**
	 * Whether autocomplete is enabled
	 */
	enabled: boolean

	/**
	 * Provider type for autocomplete service
	 */
	provider?: QaxAutocompleteProvider

	/**
	 * API key for the autocomplete service
	 */
	apiKey?: string

	/**
	 * Base URL for the autocomplete API
	 */
	apiBaseUrl?: string

	/**
	 * Model ID to use for autocomplete
	 */
	modelId?: string

	/**
	 * Maximum number of tokens for completion
	 */
	maxTokens?: number

	/**
	 * Temperature for completion generation
	 */
	temperature?: number

	/**
	 * Request timeout in milliseconds
	 */
	requestTimeoutMs?: number

	/**
	 * Whether to use prompt caching
	 */
	usePromptCache?: boolean

	/**
	 * Custom headers for API requests
	 */
	customHeaders?: Record<string, string>

	/**
	 * Debounce time in milliseconds for autocomplete requests
	 */
	debounceMs?: number

	/**
	 * Completion filtering settings
	 */
	filter?: {
		enabled?: boolean
		filterWhitespaceOnly?: boolean
		filterRepeatedContent?: boolean
		filterStrings?: string[]
	}

	/**
	 * FIM-specific settings
	 */
	fim?: {
		/**
		 * API key for FIM service
		 */
		apiKey?: string

		/**
		 * Base URL for FIM API
		 */
		baseUrl?: string

		/**
		 * Model ID for FIM service
		 */
		modelId?: string
	}
}

/**
 * Default qax autocomplete settings
 */
export const DEFAULT_QAX_AUTOCOMPLETE_SETTINGS: QaxAutocompleteSettings = {
	enabled: true,
	provider: "openai",
	apiBaseUrl: "https://aip.b.qianxin-inc.cn/v2", // Default to OpenRouter, but supports any OpenAI-compatible API
	modelId: "Qwen3-Coder-480B-A35B-Instruct",
	maxTokens: 1000,
	temperature: 0.1,
	requestTimeoutMs: 30000,
	usePromptCache: false,
	customHeaders: {},
	debounceMs: 300, // 300ms debounce by default
	filter: {
		enabled: true,
		filterWhitespaceOnly: true,
		filterRepeatedContent: true,
		filterStrings: ["{{FILL_HERE}}", "{{TODO}}", "{{PLACEHOLDER}}"],
	},
	fim: {
		baseUrl: "http://kubemlsvc.qianxin-inc.cn/svc-d70rsdpypzxs/v1/completions",
		apiKey: "",
		modelId: "Qwen7b",
	},
}

/**
 * Validates qax autocomplete settings
 */
export function validateQaxAutocompleteSettings(settings: Partial<QaxAutocompleteSettings>): string[] {
	const errors: string[] = []

	if (settings.enabled) {
		if (settings.provider === "fim") {
			// For FIM provider, check FIM-specific settings
			if (!settings.fim?.apiKey) {
				errors.push("FIM API key is required when FIM provider is enabled")
			}
			if (!settings.fim?.baseUrl) {
				errors.push("FIM base URL is required when FIM provider is enabled")
			} else if (!isValidUrl(settings.fim.baseUrl)) {
				errors.push("Invalid FIM base URL")
			}
		} else {
			// For OpenAI provider, check general API key
			if (!settings.apiKey) {
				errors.push("API key is required when autocomplete is enabled")
			}
		}
	}

	if (settings.apiBaseUrl && !isValidUrl(settings.apiBaseUrl)) {
		errors.push("Invalid API base URL")
	}

	if (settings.maxTokens && (settings.maxTokens < 1 || settings.maxTokens > 10000)) {
		errors.push("Max tokens must be between 1 and 10000")
	}

	if (settings.temperature && (settings.temperature < 0 || settings.temperature > 2)) {
		errors.push("Temperature must be between 0 and 2")
	}

	if (settings.requestTimeoutMs && (settings.requestTimeoutMs < 1000 || settings.requestTimeoutMs > 300000)) {
		errors.push("Request timeout must be between 1000ms and 300000ms")
	}

	if (settings.debounceMs && (settings.debounceMs < 0 || settings.debounceMs > 5000)) {
		errors.push("Debounce time must be between 0ms and 5000ms")
	}

	return errors
}

/**
 * Simple URL validation
 */
function isValidUrl(url: string): boolean {
	try {
		new URL(url)
		return true
	} catch {
		return false
	}
}
