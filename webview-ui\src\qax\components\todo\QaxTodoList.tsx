import React, { useState, useMemo, useCallback, useEffect, useRef } from "react"
import { VSCodeButton } from "@vscode/webview-ui-toolkit/react"
import styled from "styled-components"
import { QaxTodoItem, QaxTodoStatus, QaxTodoListProps, QaxTodoItemComponentProps, QaxTodoStats } from "./types/QaxTodoTypes"
import { QaxTodoServiceClient } from "@/services/grpc-client"
import * as proto from "@shared/proto"

// Styled components with Qax prefix
const QaxTodoContainer = styled.div<{ isExpanded: boolean }>`
	position: relative;
	background: var(--vscode-input-background);
	border: 1px solid var(--vscode-input-border);
	border-radius: 2px 2px 0 0;
	margin: 0 15px;
	margin-bottom: 0;
	transition: all 0.2s ease-in-out;
	max-height: ${(props) => (props.isExpanded ? "280px" : "24px")};
	overflow: hidden;
	box-sizing: border-box;
`

const QaxTodoHeader = styled.div<{ isExpanded: boolean }>`
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0px 8px;
	background: var(--vscode-input-background);
	border-bottom: ${(props) => (props.isExpanded ? "1px solid var(--vscode-input-border)" : "none")};
	user-select: none;
	min-height: 24px;
`

const QaxTodoHeaderLeft = styled.div`
	display: flex;
	align-items: center;
	gap: 0px;
`

const QaxTodoHeaderCenter = styled.div`
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 12px;
	font-weight: 500;
	color: var(--vscode-foreground);
`

const QaxTodoHeaderRight = styled.div`
	display: flex;
	align-items: center;
	gap: 4px;
`

const QaxHeaderButton = styled(VSCodeButton)`
	padding: 0 !important;
	height: 20px !important;
	min-width: 20px !important;
	margin: 0 !important;
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
`

const QaxTodoListContent = styled.div`
	padding: 8px 12px 12px 12px;
	max-height: 240px;
	overflow-y: auto;

	/* 确保滚动条样式 */
	&::-webkit-scrollbar {
		width: 6px;
	}

	&::-webkit-scrollbar-track {
		background: var(--vscode-scrollbarSlider-background);
	}

	&::-webkit-scrollbar-thumb {
		background: var(--vscode-scrollbarSlider-background);
		border-radius: 3px;
	}

	&::-webkit-scrollbar-thumb:hover {
		background: var(--vscode-scrollbarSlider-hoverBackground);
	}
`

const QaxTodoItemContainer = styled.div`
	display: flex;
	align-items: center;
	gap: 8px;
	padding: 4px 0;
	border-bottom: 1px solid var(--vscode-widget-border);

	&:last-child {
		border-bottom: none;
	}
`

const QaxTodoIcon = styled.div<{ status: QaxTodoStatus }>`
	width: 16px;
	height: 16px;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	color: ${(props) => {
		switch (props.status) {
			case "completed":
				return "var(--vscode-testing-iconPassed)"
			case "in_progress":
				return "var(--vscode-testing-iconQueued)"
			default:
				return "var(--vscode-foreground)"
		}
	}};
`

const QaxTodoContent = styled.div`
	flex: 1;
	min-width: 0;
`

interface QaxTodoTitleProps {
	status: QaxTodoStatus
}

const QaxTodoTitle = styled.div<QaxTodoTitleProps>`
	font-size: 12px;
	color: ${(props) => {
		switch (props.status) {
			case "completed":
				return "var(--vscode-testing-iconPassed)" // 绿色
			case "in_progress":
				return "var(--vscode-testing-iconQueued)" // 黄色
			default:
				return "var(--vscode-foreground)" // 默认颜色
		}
	}};
	text-decoration: ${(props) => (props.status === "completed" ? "line-through" : "none")};
	opacity: ${(props) => (props.status === "completed" ? 0.8 : 1)};
`

const QaxTodoActions = styled.div`
	display: flex;
	align-items: center;
	gap: 2px;
	opacity: 0;
	transition: opacity 0.2s;

	${QaxTodoItemContainer}:hover & {
		opacity: 1;
	}
`

const QaxTodoActionButton = styled(VSCodeButton)`
	padding: 0 !important;
	height: 12px !important;
	min-width: 12px !important;
	margin: 0 !important;
	display: flex !important;
	align-items: center !important;
	justify-content: center !important;
	font-size: 10px !important;
`

const QaxTodoEmptyState = styled.div`
	text-align: center;
	color: var(--vscode-descriptionForeground);
	font-size: 12px;
	padding: 20px;
`

// 单独的 QaxTodoItem 组件，使用 React.memo 优化渲染
const QaxTodoItemComponent = React.memo<QaxTodoItemComponentProps>(
	({ todo, onStatusChange, onDelete }) => {
		const getQaxTodoIcon = (status: QaxTodoStatus) => {
			switch (status) {
				case "completed":
					return "codicon-check"
				case "in_progress":
					return "codicon-sync"
				default:
					return "codicon-circle-outline"
			}
		}

		return (
			<QaxTodoItemContainer key={todo.id}>
				<QaxTodoIcon status={todo.status} onClick={() => onStatusChange(todo.id)}>
					<span className={`codicon ${getQaxTodoIcon(todo.status)}`} />
				</QaxTodoIcon>
				<QaxTodoContent>
					<QaxTodoTitle status={todo.status}>{todo.content}</QaxTodoTitle>
				</QaxTodoContent>
				<QaxTodoActions className="task-actions">
					<QaxTodoActionButton appearance="icon" onClick={() => onDelete(todo.id)} aria-label="Delete todo">
						<span className="codicon codicon-trash" />
					</QaxTodoActionButton>
				</QaxTodoActions>
			</QaxTodoItemContainer>
		)
	},
	(prevProps, nextProps) => {
		// 自定义比较函数：只有当 todo 的内容真正发生变化时才重新渲染
		return (
			prevProps.todo.id === nextProps.todo.id &&
			prevProps.todo.content === nextProps.todo.content &&
			prevProps.todo.status === nextProps.todo.status
		)
	},
)

const QaxTodoList: React.FC<QaxTodoListProps> = ({ todos = [], onTodoUpdate }) => {
	const [isExpanded, setIsExpanded] = useState(false)
	const [internalTodos, setInternalTodos] = useState<QaxTodoItem[]>([])
	const prevTodosRef = useRef<QaxTodoItem[]>([])

	const toggleExpanded = () => {
		setIsExpanded(!isExpanded)
	}

	// 增量更新逻辑：只更新发生变化的任务项
	useEffect(() => {
		const prevTodos = prevTodosRef.current
		const newTodos = todos

		// 如果是第一次加载，直接设置
		if (prevTodos.length === 0) {
			setInternalTodos([...newTodos])
			prevTodosRef.current = [...newTodos]
			return
		}

		// 创建当前任务的映射
		const currentTodoMap = new Map(internalTodos.map((todo) => [todo.id, todo]))

		// 检查是否需要完全重建列表（长度变化或顺序变化）
		const needsRebuild =
			prevTodos.length !== newTodos.length || newTodos.some((todo, index) => prevTodos[index]?.id !== todo.id)

		if (needsRebuild) {
			setInternalTodos([...newTodos])
			prevTodosRef.current = [...newTodos]
			return
		}

		// 增量更新：只更新发生变化的任务
		let hasChanges = false
		const updatedTodos = [...internalTodos]

		for (let i = 0; i < newTodos.length; i++) {
			const newTodo = newTodos[i]
			const prevTodo = prevTodos[i]
			const currentTodo = currentTodoMap.get(newTodo.id)

			// 如果任务发生了变化（相对于之前的状态）
			if (prevTodo && currentTodo && (prevTodo.content !== newTodo.content || prevTodo.status !== newTodo.status)) {
				updatedTodos[i] = { ...newTodo }
				hasChanges = true
				console.log(`[QaxTodoList] 增量更新任务: ${newTodo.id}, 状态: ${prevTodo.status} -> ${newTodo.status}`)
			}
		}

		// 只有在有变化时才更新状态
		if (hasChanges) {
			setInternalTodos(updatedTodos)
		}

		prevTodosRef.current = [...newTodos]
	}, [todos]) // 移除 internalTodos 依赖，避免循环更新

	// 使用 useMemo 缓存计算结果
	const qaxTodoStats = useMemo((): QaxTodoStats => {
		const completed = internalTodos.filter((t) => t.status === "completed").length
		const total = internalTodos.length
		return { completed, total }
	}, [internalTodos])

	const handleAddQaxTodo = useCallback(async () => {
		const content = prompt("Enter todo item:")
		if (content && content.trim()) {
			const newTodo: QaxTodoItem = {
				id: Date.now().toString(),
				content: content.trim(),
				status: "pending",
			}
			const updatedTodos = [...internalTodos, newTodo]

			// 立即更新内部状态
			setInternalTodos(updatedTodos)

			// 通知父组件
			onTodoUpdate?.(updatedTodos)

			// Send to backend using gRPC client
			await QaxTodoServiceClient.addQaxTodoItem(
				proto.cline.QaxAddTodoItemRequest.create({
					metadata: proto.cline.Metadata.create({}),
					content: content.trim(),
					status: proto.cline.QaxTodoStatus.QAX_TODO_STATUS_PENDING,
				}),
			)
		}
	}, [internalTodos, onTodoUpdate])

	const handleToggleQaxTodoStatus = useCallback(
		async (todoId: string) => {
			const todo = internalTodos.find((t) => t.id === todoId)
			if (!todo) return

			let newStatus: QaxTodoStatus
			switch (todo.status) {
				case "pending":
					newStatus = "in_progress"
					break
				case "in_progress":
					newStatus = "completed"
					break
				case "completed":
					newStatus = "pending"
					break
				default:
					newStatus = "pending"
			}

			const updatedTodos = internalTodos.map((t) => (t.id === todoId ? { ...t, status: newStatus } : t))

			// 立即更新内部状态
			setInternalTodos(updatedTodos)

			// 通知父组件
			onTodoUpdate?.(updatedTodos)

			// Send to backend using gRPC client
			await QaxTodoServiceClient.updateQaxTodoItemStatus(
				proto.cline.QaxUpdateTodoItemStatusRequest.create({
					metadata: proto.cline.Metadata.create({}),
					todoId: todoId,
					status: convertStatusToProto(newStatus),
				}),
			)
		},
		[internalTodos, onTodoUpdate],
	)

	const handleDeleteQaxTodo = useCallback(
		async (todoId: string) => {
			if (confirm("Are you sure you want to delete this todo item?")) {
				const updatedTodos = internalTodos.filter((todo) => todo.id !== todoId)

				// 立即更新内部状态
				setInternalTodos(updatedTodos)

				// 通知父组件
				onTodoUpdate?.(updatedTodos)

				// Send to backend using gRPC client
				await QaxTodoServiceClient.deleteQaxTodoItem(
					proto.cline.QaxDeleteTodoItemRequest.create({
						metadata: proto.cline.Metadata.create({}),
						todoId: todoId,
					}),
				)
			}
		},
		[internalTodos, onTodoUpdate],
	)

	// 使用 useMemo 缓存渲染的组件列表，只有当 internalTodos 数组真正变化时才重新创建
	const renderedQaxTodos = useMemo(() => {
		return internalTodos.map((todo) => (
			<QaxTodoItemComponent
				key={todo.id}
				todo={todo}
				onStatusChange={handleToggleQaxTodoStatus}
				onDelete={handleDeleteQaxTodo}
			/>
		))
	}, [internalTodos, handleToggleQaxTodoStatus, handleDeleteQaxTodo])

	return (
		<QaxTodoContainer isExpanded={isExpanded}>
			<QaxTodoHeader isExpanded={isExpanded}>
				<QaxTodoHeaderLeft>
					<QaxHeaderButton appearance="icon" onClick={toggleExpanded} aria-label={isExpanded ? "Collapse" : "Expand"}>
						<span
							className={`codicon ${isExpanded ? "codicon-chevron-down" : "codicon-chevron-right"}`}
							style={{ fontSize: "12px" }}
						/>
					</QaxHeaderButton>
					<span className="codicon codicon-checklist" style={{ fontSize: "12px" }} />
				</QaxTodoHeaderLeft>

				<QaxTodoHeaderCenter>
					任务列表({qaxTodoStats.completed}/{qaxTodoStats.total})
				</QaxTodoHeaderCenter>

				<QaxTodoHeaderRight>
					<QaxHeaderButton appearance="icon" onClick={handleAddQaxTodo} aria-label="Add todo">
						<span className="codicon codicon-add" style={{ fontSize: "12px" }} />
					</QaxHeaderButton>
				</QaxTodoHeaderRight>
			</QaxTodoHeader>

			{isExpanded && (
				<QaxTodoListContent>
					{internalTodos.length === 0 ? (
						<QaxTodoEmptyState>No todo items yet. Click + to add one.</QaxTodoEmptyState>
					) : (
						renderedQaxTodos
					)}
				</QaxTodoListContent>
			)}
		</QaxTodoContainer>
	)
}

/**
 * 转换前端状态到 proto 状态
 */
function convertStatusToProto(status: QaxTodoStatus): proto.cline.QaxTodoStatus {
	switch (status) {
		case "completed":
			return proto.cline.QaxTodoStatus.QAX_TODO_STATUS_COMPLETED
		case "in_progress":
			return proto.cline.QaxTodoStatus.QAX_TODO_STATUS_IN_PROGRESS
		case "pending":
		default:
			return proto.cline.QaxTodoStatus.QAX_TODO_STATUS_PENDING
	}
}

export default QaxTodoList
