# Todo List Partial Handling Fix

## 问题描述

在流式响应处理中，`update_todo_list` 工具存在一个关键问题：当模型开始返回工具调用时，系统会在参数内容完整接收之前就调用 `updateTodoListTool()` 函数，导致参数为空或不完整。

## 问题根源

### 流式响应的处理机制

1. **解析器行为**：当解析器遇到 `<update_todo_list>` 标签时，立即创建一个 `partial: true` 的工具块
2. **立即执行**：`presentAssistantMessage()` 函数会立即处理每个解析出的内容块，包括 partial 状态的工具调用
3. **参数不完整**：此时 `<todos>` 参数可能还没有完全接收到

### 原始实现的问题

```typescript
// 原始的有问题的实现
case "update_todo_list": {
    try {
        const { updateTodoListTool } = await import("@core/tools/updateTodoListTool")
        await updateTodoListTool(
            this.taskState,
            block,  // block.partial 可能为 true，参数可能为空
            // ...
        )
    }
}
```

## 解决方案

### 1. 参考其他工具的处理模式

通过分析其他工具（如 `ask_followup_question`、`new_task` 等）的实现，发现标准模式是：

```typescript
case "some_tool": {
    const param: string | undefined = block.params.param
    try {
        if (block.partial) {
            // 只显示UI，使用 removeClosingTag 处理可能不完整的参数
            await this.ask("tool", partialMessage, block.partial).catch(() => {})
            break
        } else {
            // 进行参数验证和实际执行
            if (!param) {
                // 错误处理
            }
            // 实际工具逻辑
        }
    }
}
```

### 2. 修复后的实现

#### ToolExecutor 中的处理

```typescript
case "update_todo_list": {
    const todos: string | undefined = block.params.todos
    try {
        if (block.partial) {
            // 显示部分UI，不执行实际逻辑
            const partialMessage = JSON.stringify({
                tool: "updateTodoList",
                todos: this.removeClosingTag(block, "todos", todos),
            })
            await this.ask("tool", partialMessage, block.partial).catch(() => {})
            break
        } else {
            // 只有在完整内容时才执行工具逻辑
            if (!todos) {
                this.taskState.consecutiveMistakeCount++
                this.pushToolResult(await this.sayAndCreateMissingParamError("update_todo_list", "todos"), block)
                await this.saveCheckpoint()
                break
            }
            
            const { updateTodoListTool } = await import("@core/tools/updateTodoListTool")
            await updateTodoListTool(/* ... */)
            await this.saveCheckpoint()
            break
        }
    }
}
```

#### updateTodoListTool 函数的简化

```typescript
export async function updateTodoListTool(
    taskState: any,
    block: any,  // 现在保证 block.partial 总是 false
    // ...
) {
    // 移除了 block.partial 的检查，因为现在只在完整内容时调用
    try {
        const todosRaw = block.params.todos
        // 直接进行解析和验证
        let todos: TodoItem[] = parseMarkdownChecklist(todosRaw || "")
        // ...
    }
}
```

## 修复效果

### 1. 流式处理流程

```mermaid
sequenceDiagram
    participant Model as 模型流式响应
    participant Parser as 解析器
    participant ToolExecutor as ToolExecutor
    participant TodoTool as updateTodoListTool
    
    Model->>Parser: <update_todo_list>
    Parser->>ToolExecutor: {partial: true, params: {}}
    ToolExecutor->>ToolExecutor: 显示部分UI
    Note over ToolExecutor: 不调用 updateTodoListTool
    
    Model->>Parser: <todos>- [ ] Task 1</todos>
    Parser->>ToolExecutor: {partial: true, params: {todos: "- [ ] Task 1"}}
    ToolExecutor->>ToolExecutor: 更新UI显示
    Note over ToolExecutor: 仍不调用 updateTodoListTool
    
    Model->>Parser: </update_todo_list>
    Parser->>ToolExecutor: {partial: false, params: {todos: "- [ ] Task 1"}}
    ToolExecutor->>TodoTool: updateTodoListTool(完整参数)
    TodoTool->>ToolExecutor: 执行成功
```

### 2. 关键改进

1. **参数完整性保证**：`updateTodoListTool` 现在只在参数完整时被调用
2. **UI 流式更新**：partial 状态时仍然可以显示流式UI更新
3. **错误处理改进**：在 ToolExecutor 层面就进行参数验证
4. **性能优化**：避免了不必要的函数调用和解析操作

### 3. 测试验证

创建了专门的测试文件 `todo-partial-handling.test.ts` 来验证：

- ✅ Partial 块不会触发 `updateTodoListTool` 调用
- ✅ 只有完整块才会执行实际工具逻辑
- ✅ 错误处理在正确的层级进行
- ✅ 流式UI更新仍然正常工作

## 总结

这个修复解决了流式响应中工具调用时机的根本问题：

1. **问题**：在参数不完整时就调用工具函数
2. **原因**：没有正确处理 `block.partial` 状态
3. **解决**：在 ToolExecutor 层面处理 partial 状态，只在完整时调用工具函数
4. **效果**：保证了参数完整性，同时保持了流式UI更新的用户体验

这个修复模式也可以应用到其他可能存在类似问题的工具上，确保整个系统的流式响应处理更加健壮。
