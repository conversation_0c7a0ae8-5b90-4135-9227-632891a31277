import Tooltip from "@/components/common/Tooltip"
import { QaxUtilsServiceClient } from "@/services/grpc-client"
import { StringRequest } from "@shared/proto/common"
import { VSCodeButton } from "@vscode/webview-ui-toolkit/react"
import React, { useState } from "react"

interface QaxEnhancePromptButtonProps {
	inputValue: string
	setInputValue: (value: string) => void
	sendingDisabled: boolean
}

const QaxEnhancePromptButton: React.FC<QaxEnhancePromptButtonProps> = ({ inputValue, setInputValue, sendingDisabled }) => {
	const [isEnhancing, setIsEnhancing] = useState(false)

	const handleEnhancePrompt = async () => {
		if (!inputValue.trim() || sendingDisabled || isEnhancing) {
			return
		}

		setIsEnhancing(true)
		try {
			const response = await QaxUtilsServiceClient.enhancePrompt(StringRequest.create({ value: inputValue }))
			if (response.value) {
				setInputValue(response.value)
			} else {
				console.warn("Enhanced prompt not available")
			}
		} catch (error) {
			console.error("Failed to enhance prompt:", error)
		} finally {
			setIsEnhancing(false)
		}
	}

	const isDisabled = sendingDisabled || !inputValue.trim() || isEnhancing

	return (
		<Tooltip tipText="Qax Enhance Prompt">
			<VSCodeButton
				appearance="icon"
				aria-label="Qax Enhance Prompt"
				disabled={isDisabled}
				onClick={handleEnhancePrompt}
				style={{ padding: "0px 0px", height: "20px" }}>
				<div className="flex items-center gap-1 text-xs whitespace-nowrap min-w-0 w-full">
					{isEnhancing ? (
						<span
							className="codicon codicon-loading codicon-modifier-spin flex items-center"
							style={{ fontSize: "12.5px", marginBottom: 1 }}
						/>
					) : (
						<span style={{ fontSize: "14px", marginBottom: 1 }}>✨</span>
					)}
				</div>
			</VSCodeButton>
		</Tooltip>
	)
}

export default QaxEnhancePromptButton
