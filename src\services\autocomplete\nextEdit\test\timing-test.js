/**
 * Test script to verify timing enhancement in NextEdit
 *
 * This script validates that the timing logs are properly implemented
 * and provide comprehensive performance metrics.
 */

const fs = require("fs")
const path = require("path")

function validateTimingEnhancement() {
	console.log("⏰ Validating NextEdit Timing Enhancement...\n")

	const results = {
		total: 0,
		passed: 0,
		failed: 0,
		tests: [],
	}

	// Test 1: Verify comprehensive timing variables are declared
	results.total++
	try {
		const enginePath = path.join(__dirname, "..", "NextEditRecommendationEngine.ts")
		const content = fs.readFileSync(enginePath, "utf8")

		const hasTimingVars =
			content.includes("requestStartTime = Date.now()") &&
			content.includes("modelCallStartTime = Date.now()") &&
			content.includes("firstChunkTime: number | null = null") &&
			content.includes("lastChunkTime: number | null = null") &&
			content.includes("chunkCount = 0")

		if (hasTimingVars) {
			results.passed++
			results.tests.push({ name: "Comprehensive timing variables declared", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Comprehensive timing variables declared", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Comprehensive timing variables declared", status: "ERROR", error: error.message })
	}

	// Test 2: Verify detailed performance metrics calculation
	results.total++
	try {
		const enginePath = path.join(__dirname, "..", "NextEditRecommendationEngine.ts")
		const content = fs.readFileSync(enginePath, "utf8")

		const hasMetricsCalc =
			content.includes("totalModelTime = modelCallEndTime - modelCallStartTime") &&
			content.includes("totalRequestTime = modelCallEndTime - requestStartTime") &&
			content.includes("timeToFirstChunk = firstChunkTime ? firstChunkTime - modelCallStartTime : 0") &&
			content.includes("streamingTime = lastChunkTime && firstChunkTime ? lastChunkTime - firstChunkTime : 0")

		if (hasMetricsCalc) {
			results.passed++
			results.tests.push({ name: "Detailed performance metrics calculation", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Detailed performance metrics calculation", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Detailed performance metrics calculation", status: "ERROR", error: error.message })
	}

	// Test 3: Verify streaming response monitoring
	results.total++
	try {
		const enginePath = path.join(__dirname, "..", "NextEditRecommendationEngine.ts")
		const content = fs.readFileSync(enginePath, "utf8")

		const hasStreamingMonitoring =
			content.includes("if (firstChunkTime === null)") &&
			content.includes("const timeToFirstChunk = chunkTime - modelCallStartTime") &&
			content.includes("First chunk received after") &&
			content.includes("chunkCount++")

		if (hasStreamingMonitoring) {
			results.passed++
			results.tests.push({ name: "Streaming response monitoring", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Streaming response monitoring", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Streaming response monitoring", status: "ERROR", error: error.message })
	}

	// Test 4: Verify comprehensive logging output
	results.total++
	try {
		const enginePath = path.join(__dirname, "..", "NextEditRecommendationEngine.ts")
		const content = fs.readFileSync(enginePath, "utf8")

		const hasComprehensiveLogging =
			content.includes("Performance Metrics:") &&
			content.includes("Total request time:") &&
			content.includes("Model processing time:") &&
			content.includes("Time to first chunk:") &&
			content.includes("Streaming time:") &&
			content.includes("Total chunks received:") &&
			content.includes("Response length:") &&
			content.includes("Throughput:")

		if (hasComprehensiveLogging) {
			results.passed++
			results.tests.push({ name: "Comprehensive logging output", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Comprehensive logging output", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Comprehensive logging output", status: "ERROR", error: error.message })
	}

	// Test 5: Verify throughput calculation
	results.total++
	try {
		const enginePath = path.join(__dirname, "..", "NextEditRecommendationEngine.ts")
		const content = fs.readFileSync(enginePath, "utf8")

		const hasThroughputCalc =
			content.includes("if (totalModelTime > 0)") &&
			content.includes("const throughput = responseContent.length / totalModelTime * 1000") &&
			content.includes("chars/sec")

		if (hasThroughputCalc) {
			results.passed++
			results.tests.push({ name: "Throughput calculation", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Throughput calculation", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Throughput calculation", status: "ERROR", error: error.message })
	}

	// Test 6: Verify response parsing time tracking
	results.total++
	try {
		const enginePath = path.join(__dirname, "..", "NextEditRecommendationEngine.ts")
		const content = fs.readFileSync(enginePath, "utf8")

		const hasParsingTime =
			content.includes("parseStartTime = Date.now()") &&
			content.includes("parseTime = Date.now() - parseStartTime") &&
			content.includes("Response parsing took")

		if (hasParsingTime) {
			results.passed++
			results.tests.push({ name: "Response parsing time tracking", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Response parsing time tracking", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Response parsing time tracking", status: "ERROR", error: error.message })
	}

	// Test 7: Verify end-to-end time tracking
	results.total++
	try {
		const enginePath = path.join(__dirname, "..", "NextEditRecommendationEngine.ts")
		const content = fs.readFileSync(enginePath, "utf8")

		const hasEndToEndTime = content.includes("Total end-to-end time:") && content.includes("Date.now() - requestStartTime")

		if (hasEndToEndTime) {
			results.passed++
			results.tests.push({ name: "End-to-end time tracking", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "End-to-end time tracking", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "End-to-end time tracking", status: "ERROR", error: error.message })
	}

	// Calculate success rate
	const successRate = (results.passed / results.total) * 100

	// Print results
	console.log("📊 Timing Enhancement Validation Results:")
	console.log("==========================================")

	results.tests.forEach((test) => {
		const icon = test.status === "PASS" ? "✅" : test.status === "FAIL" ? "❌" : "⚠️"
		console.log(`${icon} ${test.name}`)
		if (test.error) {
			console.log(`   Error: ${test.error}`)
		}
	})

	console.log("\n📈 Summary:")
	console.log(`   Total Tests: ${results.total}`)
	console.log(`   Passed: ${results.passed}`)
	console.log(`   Failed: ${results.failed}`)
	console.log(`   Success Rate: ${successRate.toFixed(1)}%`)

	const success = successRate >= 90 // 90% threshold for timing enhancement
	console.log(`\n🎯 Result: ${success ? "🎉 TIMING ENHANCEMENT IMPLEMENTED" : "⚠️ NEEDS MORE WORK"}`)

	if (success) {
		console.log("\n✨ Timing enhancement has been successfully implemented!")
		console.log("\n⏰ Enhanced Timing Features:")
		console.log("   1. ✅ Comprehensive timing variables")
		console.log("   2. ✅ Detailed performance metrics calculation")
		console.log("   3. ✅ Streaming response monitoring")
		console.log("   4. ✅ Comprehensive logging output")
		console.log("   5. ✅ Throughput calculation")
		console.log("   6. ✅ Response parsing time tracking")
		console.log("   7. ✅ End-to-end time tracking")
		console.log("")
		console.log("📊 Performance Metrics Now Tracked:")
		console.log("   • 📏 Total request time (ms)")
		console.log("   • 🤖 Model processing time (ms)")
		console.log("   • ⚡ Time to first chunk (ms)")
		console.log("   • 📡 Streaming time (ms)")
		console.log("   • 📦 Total chunks received")
		console.log("   • 📝 Response length (characters)")
		console.log("   • 🚀 Throughput (chars/sec)")
		console.log("   • 🔧 Response parsing time (ms)")
		console.log("   • 📊 End-to-end time (ms)")
		console.log("")
		console.log("🔍 Example Log Output:")
		console.log("   🚀📊 NextEdit: Performance Metrics:")
		console.log("      📏 Total request time: 4500ms")
		console.log("      🤖 Model processing time: 4400ms")
		console.log("      ⚡ Time to first chunk: 1250ms")
		console.log("      📡 Streaming time: 3150ms")
		console.log("      📦 Total chunks received: 45")
		console.log("      📝 Response length: 2847 characters")
		console.log("      🚀 Throughput: 647.0 chars/sec")
		console.log("")
		console.log("📝 Usage Instructions:")
		console.log("   1. Enable NextEdit in VS Code")
		console.log("   2. Make code changes to trigger suggestions")
		console.log("   3. Check VS Code Developer Console for timing logs")
		console.log("   4. Monitor performance metrics for optimization")
	} else {
		console.log("\n🔧 Some timing enhancement features need additional work.")
	}

	return success
}

// Run validation
if (require.main === module) {
	validateTimingEnhancement()
}

module.exports = { validateTimingEnhancement }
