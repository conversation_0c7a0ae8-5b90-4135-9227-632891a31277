/**
 * 相关符号发现功能与 AutocompleteProvider 的集成示例
 */

import * as vscode from "vscode"
import { discoverRelatedSymbols, SymbolDefinition } from "./RelatedSymbolsDiscovery"

/**
 * 智能上下文提取器
 * 结合相关符号发现功能，为自动补全提供更智能的上下文
 */
export class IntelligentContextExtractor {
	private symbolCache = new Map<string, SymbolDefinition[]>()
	private cacheTimeout = 5 * 60 * 1000 // 5分钟缓存

	/**
	 * 为指定文件提取智能上下文
	 * @param document 当前文档
	 * @param position 光标位置
	 * @param maxContextLines 最大上下文行数
	 * @returns 包含相关符号的智能上下文
	 */
	async extractIntelligentContext(
		document: vscode.TextDocument,
		position: vscode.Position,
		maxContextLines: number = 50,
	): Promise<string> {
		const filePath = document.uri.fsPath
		const code = document.getText()

		// 获取相关符号
		const relatedSymbols = await this.getRelatedSymbols(filePath, code)

		// 构建智能上下文
		const context = await this.buildIntelligentContext(document, position, relatedSymbols, maxContextLines)

		return context
	}

	/**
	 * 获取相关符号（带缓存）
	 */
	private async getRelatedSymbols(filePath: string, code: string): Promise<SymbolDefinition[]> {
		const cacheKey = `${filePath}:${this.getCodeHash(code)}`

		// 检查缓存
		if (this.symbolCache.has(cacheKey)) {
			return this.symbolCache.get(cacheKey)!
		}

		try {
			// 发现相关符号
			const symbols = await discoverRelatedSymbols(filePath, code, 20)

			// 缓存结果
			this.symbolCache.set(cacheKey, symbols)

			// 设置缓存过期
			setTimeout(() => {
				this.symbolCache.delete(cacheKey)
			}, this.cacheTimeout)

			return symbols
		} catch (error) {
			console.warn("Failed to discover related symbols:", error)
			return []
		}
	}

	/**
	 * 构建智能上下文
	 */
	private async buildIntelligentContext(
		document: vscode.TextDocument,
		position: vscode.Position,
		relatedSymbols: SymbolDefinition[],
		maxContextLines: number,
	): Promise<string> {
		const contextParts: string[] = []

		// 1. 当前文件的局部上下文
		const localContext = this.extractLocalContext(document, position, 20)
		contextParts.push("// === 当前文件上下文 ===")
		contextParts.push(localContext)

		// 2. 相关符号定义（按重要性排序）
		if (relatedSymbols.length > 0) {
			contextParts.push("\n// === 相关符号定义 ===")

			// 按来源和分数组织符号
			const organizedSymbols = this.organizeSymbolsByImportance(relatedSymbols)

			let addedLines = 0
			const maxSymbolLines = maxContextLines - localContext.split("\n").length

			for (const symbolGroup of organizedSymbols) {
				if (addedLines >= maxSymbolLines) break

				contextParts.push(`\n// --- ${symbolGroup.title} ---`)

				for (const symbol of symbolGroup.symbols) {
					if (addedLines >= maxSymbolLines) break

					const symbolContext = this.formatSymbolForContext(symbol)
					const symbolLines = symbolContext.split("\n").length

					if (addedLines + symbolLines <= maxSymbolLines) {
						contextParts.push(symbolContext)
						addedLines += symbolLines
					}
				}
			}
		}

		return contextParts.join("\n")
	}

	/**
	 * 提取局部上下文
	 */
	private extractLocalContext(document: vscode.TextDocument, position: vscode.Position, contextLines: number): string {
		const startLine = Math.max(0, position.line - contextLines)
		const endLine = Math.min(document.lineCount - 1, position.line + contextLines)

		const lines: string[] = []
		for (let i = startLine; i <= endLine; i++) {
			const line = document.lineAt(i).text
			const marker = i === position.line ? " <-- 当前位置" : ""
			lines.push(`${i + 1}: ${line}${marker}`)
		}

		return lines.join("\n")
	}

	/**
	 * 按重要性组织符号
	 */
	private organizeSymbolsByImportance(symbols: SymbolDefinition[]): Array<{
		title: string
		symbols: SymbolDefinition[]
	}> {
		const groups = [
			{
				title: "导入的符号定义",
				symbols: symbols.filter((s) => s.source === "import").slice(0, 5),
			},
			{
				title: "未定义符号的可能定义",
				symbols: symbols.filter((s) => s.source === "undefined_symbol").slice(0, 3),
			},
			{
				title: "相关文件中的符号",
				symbols: symbols.filter((s) => s.source === "heuristic").slice(0, 3),
			},
			{
				title: "最近修改文件中的符号",
				symbols: symbols.filter((s) => s.source === "recent_modified").slice(0, 2),
			},
		]

		return groups.filter((group) => group.symbols.length > 0)
	}

	/**
	 * 格式化符号用于上下文
	 */
	private formatSymbolForContext(symbol: SymbolDefinition): string {
		const header = `// ${symbol.name} (${symbol.type}) from ${symbol.filePath}`
		const content = symbol.content.split("\n").slice(0, 10).join("\n") // 限制行数

		return `${header}\n${content}\n`
	}

	/**
	 * 计算代码哈希用于缓存
	 */
	private getCodeHash(code: string): string {
		// 简单的哈希函数
		let hash = 0
		for (let i = 0; i < code.length; i++) {
			const char = code.charCodeAt(i)
			hash = (hash << 5) - hash + char
			hash = hash & hash // 转换为32位整数
		}
		return hash.toString()
	}

	/**
	 * 清理缓存
	 */
	clearCache(): void {
		this.symbolCache.clear()
	}
}

/**
 * 在 AutocompleteProvider 中使用的示例方法
 */
export async function enhanceAutocompleteContext(document: vscode.TextDocument, position: vscode.Position): Promise<string> {
	const extractor = new IntelligentContextExtractor()

	try {
		const intelligentContext = await extractor.extractIntelligentContext(
			document,
			position,
			100, // 最大100行上下文
		)

		return intelligentContext
	} catch (error) {
		console.warn("Failed to enhance autocomplete context:", error)

		// 降级到基本上下文提取
		const basicContext = document.getText(
			new vscode.Range(
				new vscode.Position(Math.max(0, position.line - 20), 0),
				new vscode.Position(Math.min(document.lineCount - 1, position.line + 20), 0),
			),
		)

		return basicContext
	}
}

/**
 * 工具函数：检查符号是否与当前上下文相关
 */
export function isSymbolRelevantToContext(
	symbol: SymbolDefinition,
	currentCode: string,
	cursorPosition: vscode.Position,
): boolean {
	// 检查符号名称是否在当前代码中出现
	if (currentCode.includes(symbol.name)) {
		return true
	}

	// 检查符号类型是否与当前上下文匹配
	const currentLine = currentCode.split("\n")[cursorPosition.line] || ""

	if (symbol.type === "function" && (currentLine.includes("(") || currentLine.includes("call"))) {
		return true
	}

	if (symbol.type === "class" && (currentLine.includes("new ") || currentLine.includes("extends"))) {
		return true
	}

	if (symbol.type === "interface" && currentLine.includes(":")) {
		return true
	}

	return false
}

/**
 * 导出主要的集成类
 */
export { IntelligentContextExtractor as default }
