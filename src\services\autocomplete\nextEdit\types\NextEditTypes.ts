import * as vscode from "vscode"

/**
 * Priority levels for Next Edit suggestions
 */
export enum NextEditPriority {
	HIGH = "high",
	MEDIUM = "medium",
	LOW = "low",
}

/**
 * Types of Next Edit operations
 */
export enum NextEditType {
	ADD = "add",
	MODIFY = "modify",
	DELETE = "delete",
}

/**
 * Categories of Next Edit suggestions
 */
export enum NextEditCategory {
	COMPLETION = "completion",
	ERROR_HANDLING = "error_handling",
	TYPE_SAFETY = "type_safety",
	TESTING = "testing",
	DOCUMENTATION = "documentation",
	BUG_FIX = "bug_fix",
}

/**
 * Position information for applying suggestions
 */
export interface NextEditLocation {
	/** Unique code pattern to locate the position */
	anchor: string
	/** Where to apply the change relative to the anchor */
	position: "before" | "after" | "replace"
}

/**
 * Patch information for code changes
 */
export interface NextEditPatch {
	/** Exact content to be replaced (for modify/delete) */
	oldContent?: string
	/** New content to insert/replace with (for add/modify) */
	newContent: string
}

/**
 * Individual Next Edit suggestion
 */
export interface NextEditSuggestion {
	/** Unique identifier for the suggestion */
	id: string
	/** Type of operation */
	type: NextEditType
	/** Brief description of what to change */
	description: string
	/** Location information */
	location: NextEditLocation
	/** Patch information */
	patch: NextEditPatch
	/** Reasoning for the suggestion (optional) */
	reasoning?: string
	/** File path this suggestion applies to */
	filePath: string
	/** Timestamp when suggestion was created */
	createdAt: Date
}

/**
 * Response from AI model for Next Edit recommendations
 */
export interface NextEditAIResponse {
	suggestions: NextEditSuggestion[]
}

/**
 * Context information for generating Next Edit suggestions
 */
export interface NextEditContext {
	/** File path */
	filePath: string
	/** Programming language */
	language: string
	/** Recent changes made */
	recentChanges: string
	/** Current code content */
	currentCode: string
	/** Cursor position (optional) */
	cursorPosition?: vscode.Position
	/** Additional context */
	additionalContext?: string
	/** Error/warning context */
	errorsWarnings?: string
	/** Allowed task types */
	allowedTaskTypes?: string
}

/**
 * Configuration for Next Edit service
 */
export interface NextEditConfig {
	/** Whether Next Edit is enabled */
	enabled: boolean
	/** Debounce delay in milliseconds */
	debounceDelayMs: number
	/** Maximum number of suggestions to show */
	maxSuggestions: number
	/** Context window size for large files (lines before/after) */
	contextWindowSize: number
	/** Allowed task types */
	allowedTaskTypes: NextEditCategory[]
}

/**
 * File suggestion state
 */
export interface NextEditFileState {
	/** File path */
	filePath: string
	/** Current suggestions for this file */
	suggestions: NextEditSuggestion[]
	/** Index of currently active suggestion */
	currentSuggestionIndex: number
	/** Whether suggestions are currently being shown */
	isShowingSuggestions: boolean
	/** Last modification timestamp */
	lastModified: Date
}

/**
 * Event types for Next Edit service
 */
export enum NextEditEventType {
	SUGGESTION_APPLIED = "suggestion_applied",
	SUGGESTION_IGNORED = "suggestion_ignored",
	SUGGESTIONS_GENERATED = "suggestions_generated",
	SUGGESTIONS_CLEARED = "suggestions_cleared",
	SERVICE_ENABLED = "service_enabled",
	SERVICE_DISABLED = "service_disabled",
}

/**
 * Event data for Next Edit events
 */
export interface NextEditEvent {
	type: NextEditEventType
	filePath?: string
	suggestionId?: string
	timestamp: Date
	data?: any
}

/**
 * Callback function for Next Edit events
 */
export type NextEditEventCallback = (event: NextEditEvent) => void

/**
 * Default configuration for Next Edit service
 */
export const DEFAULT_NEXT_EDIT_CONFIG: NextEditConfig = {
	enabled: true,
	debounceDelayMs: 2000,
	maxSuggestions: 5,
	contextWindowSize: 200,
	allowedTaskTypes: [
		NextEditCategory.COMPLETION,
		NextEditCategory.ERROR_HANDLING,
		NextEditCategory.TYPE_SAFETY,
		NextEditCategory.TESTING,
		NextEditCategory.DOCUMENTATION,
		NextEditCategory.BUG_FIX,
	],
}
