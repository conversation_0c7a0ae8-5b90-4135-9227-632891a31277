# QAX Autocomplete

QAX Autocomplete is an intelligent code completion feature integrated into Cline that provides AI-powered code suggestions as you type.

## Features

- **Real-time code completion**: Get intelligent code suggestions as you type
- **Multiple language support**: Works with JavaScript, TypeScript, Python, Java, and more
- **Configurable AI models**: Choose from various AI models for code generation
- **Cost tracking**: Monitor API usage and costs in real-time
- **Context-aware suggestions**: Leverages your current code context for better suggestions

## Setup

### 1. Enable QAX Autocomplete

1. Open Cline settings by clicking the settings gear icon (⚙️) in the Cline sidebar
2. Navigate to the **Autocomplete** tab
3. Check the **Enable QAX Autocomplete** checkbox

### 2. Configure API Settings

QAX Autocomplete uses OpenAI-compatible APIs, giving you flexibility to choose from various providers:

#### Option 1: OpenRouter (Recommended)
1. Sign up at [OpenRouter](https://openrouter.ai/)
2. Get your API key from the dashboard
3. In Cline's Autocomplete settings:
   - **API Base URL**: `https://api.openrouter.ai/api/v1` (default)
   - **API Key**: Your OpenRouter API key
   - **Model ID**: `google/gemini-2.5-flash-preview-05-20` (default, or choose another)

#### Option 2: OpenAI Direct
1. Get your API key from [OpenAI](https://platform.openai.com/)
2. In Cline's Autocomplete settings:
   - **API Base URL**: `https://api.openai.com/v1`
   - **API Key**: Your OpenAI API key
   - **Model ID**: `gpt-4o-mini` or `gpt-4o`

#### Option 3: Local Models
- **Ollama**: Set API Base URL to `http://localhost:11434/v1`
- **LM Studio**: Set API Base URL to `http://localhost:1234/v1`
- **Other local servers**: Use the appropriate local endpoint

#### Option 4: Other Compatible Services
QAX Autocomplete works with any OpenAI-compatible API:
- Azure OpenAI
- Anthropic (via proxy)
- Together AI
- Fireworks AI
- Any other OpenAI-compatible endpoint

### 3. Configure Advanced Settings (Optional)

- **Max Tokens**: Maximum number of tokens for each completion (default: 1000)
- **Temperature**: Controls randomness of suggestions (0.0-2.0, default: 0.1)
- **Request Timeout**: API request timeout in milliseconds (default: 30000)
- **Use Prompt Cache**: Enable prompt caching for better performance (if supported)

## Usage

### Getting Suggestions

1. Open any code file in VS Code
2. Start typing code
3. QAX Autocomplete will automatically show suggestions as you type
4. Press `Tab` or `Enter` to accept a suggestion
5. Press `Esc` to dismiss suggestions

### Status Bar

The QAX Autocomplete status is shown in the VS Code status bar:

- **🌟 QAX Complete ($0.00)**: Active and showing current session cost
- **⚠️ QAX Complete**: Warning (usually missing API key)
- **🚫 QAX Complete**: Disabled

Click the status bar item to toggle autocomplete on/off.

### Commands

Access these commands via the Command Palette (`Ctrl+Shift+P` / `Cmd+Shift+P`):

- **Toggle QAX Autocomplete**: Enable/disable autocomplete
- **Track Accepted Suggestion**: Internal command for usage tracking

## Configuration Reference

### Basic Settings

| Setting | Description | Default |
|---------|-------------|---------|
| **Enable QAX Autocomplete** | Master on/off switch | `false` |
| **API Key** | Your API key (stored securely) | - |
| **API Base URL** | Base URL for the API service | `https://api.openrouter.ai/api/v1` |
| **Model ID** | AI model to use for completions | `google/gemini-2.5-flash-preview-05-20` |

### Advanced Settings

| Setting | Description | Default | Range |
|---------|-------------|---------|-------|
| **Max Tokens** | Maximum tokens per completion | `1000` | 1-10000 |
| **Temperature** | Randomness of suggestions | `0.1` | 0.0-2.0 |
| **Request Timeout** | API timeout in milliseconds | `30000` | 1000-300000 |
| **Use Prompt Cache** | Enable prompt caching | `false` | - |
| **Custom Headers** | Additional HTTP headers | `{}` | - |

## Supported Models

QAX Autocomplete works with various AI models. Popular choices include:

### Fast Models (Recommended for Autocomplete)
- `google/gemini-2.5-flash-preview-05-20` (default)
- `anthropic/claude-3-haiku`
- `openai/gpt-4o-mini`

### High-Quality Models
- `anthropic/claude-3-5-sonnet`
- `openai/gpt-4o`
- `google/gemini-pro`

### Local Models
- Use Ollama: Set API Base URL to `http://localhost:11434/v1`
- Use LM Studio: Set API Base URL to `http://localhost:1234/v1`

## Troubleshooting

### Autocomplete Not Working

1. **Check if enabled**: Verify QAX Autocomplete is enabled in settings
2. **Verify API key**: Ensure you have a valid API key configured
3. **Check network**: Verify internet connectivity
4. **Model availability**: Ensure the selected model is available
5. **Check status bar**: Look for warning indicators

### Common Issues

#### "Warning" Status Bar
- **Cause**: Missing or invalid API key
- **Solution**: Configure a valid API key in settings

#### No Suggestions Appearing
- **Cause**: Model not responding or network issues
- **Solution**: Try a different model or check network connectivity

#### Slow Suggestions
- **Cause**: Model is slow or network latency
- **Solution**: Try a faster model or increase timeout

#### High Costs
- **Cause**: Using expensive models or high token limits
- **Solution**: Switch to a more cost-effective model or reduce max tokens

### Getting Help

1. Check the VS Code Developer Console for error messages
2. Verify your API service status
3. Try with a different model
4. Restart VS Code
5. Report issues on the Cline GitHub repository

## Privacy and Security

- **API Keys**: Stored securely using VS Code's secret storage
- **Code Context**: Only the immediate code context is sent to the AI service
- **No Data Storage**: Your code is not stored by QAX Autocomplete
- **Configurable**: You control what data is sent via your choice of API service

## Cost Management

- **Real-time Tracking**: Monitor costs in the status bar
- **Session Totals**: See cumulative costs for your current session
- **Model Selection**: Choose cost-effective models for routine work
- **Token Limits**: Set appropriate max token limits to control costs

## Best Practices

1. **Start with Fast Models**: Use quick, inexpensive models for most work
2. **Monitor Costs**: Keep an eye on the status bar cost indicator
3. **Adjust Settings**: Fine-tune temperature and token limits for your needs
4. **Use Appropriate Models**: Match model capability to task complexity
5. **Secure API Keys**: Never share your API keys or commit them to version control
