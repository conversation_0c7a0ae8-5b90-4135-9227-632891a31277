/**
 * Test script to verify highlight range and content filtering fixes
 *
 * This script validates that:
 * 1. HTML tag matching highlights the complete tag, not just partial attributes
 * 2. Suggestions with identical anchor and new_content are filtered out
 * 3. Suggestions with identical oldContent and newContent are filtered out
 */

const fs = require("fs")
const path = require("path")

function validateHighlightAndContentFiltering() {
	console.log("🎨 Validating NextEdit Highlight Range and Content Filtering...\n")

	const results = {
		total: 0,
		passed: 0,
		failed: 0,
		tests: [],
	}

	// Test 1: Verify findHtmlTagMatch method exists
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasHtmlTagMatch =
			content.includes("findHtmlTagMatch") &&
			content.includes("private findHtmlTagMatch") &&
			content.includes("html_tag_match")

		if (hasHtmlTagMatch) {
			results.passed++
			results.tests.push({ name: "findHtmlTagMatch method exists", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "findHtmlTagMatch method exists", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "findHtmlTagMatch method exists", status: "ERROR", error: error.message })
	}

	// Test 2: Verify HTML tag name extraction
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasTagNameExtraction =
			content.includes("tagNameMatch = pattern.match(/^(\\w+)\\s/)") && content.includes("const tagName = tagNameMatch[1]")

		if (hasTagNameExtraction) {
			results.passed++
			results.tests.push({ name: "HTML tag name extraction", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "HTML tag name extraction", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "HTML tag name extraction", status: "ERROR", error: error.message })
	}

	// Test 3: Verify multiple attributes matching
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasMultipleAttrs =
			content.includes("attrMatches = [...pattern.matchAll") &&
			content.includes("for (const [, attrName, attrValue] of attrMatches)") &&
			content.includes("All attributes found")

		if (hasMultipleAttrs) {
			results.passed++
			results.tests.push({ name: "Multiple attributes matching", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Multiple attributes matching", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Multiple attributes matching", status: "ERROR", error: error.message })
	}

	// Test 4: Verify complete tag range return
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasCompleteRange =
			content.includes("lineTagMatch.index") &&
			content.includes("lineTagMatch.index + tagContent.length") &&
			content.includes("tagContent = lineTagMatch[0]")

		if (hasCompleteRange) {
			results.passed++
			results.tests.push({ name: "Complete tag range return", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Complete tag range return", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Complete tag range return", status: "ERROR", error: error.message })
	}

	// Test 5: Verify anchor and new_content identical filtering
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasIdenticalFiltering =
			content.includes("anchorPattern === newContent") && content.includes("anchor and new_content are identical")

		if (hasIdenticalFiltering) {
			results.passed++
			results.tests.push({ name: "Anchor and new_content identical filtering", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Anchor and new_content identical filtering", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Anchor and new_content identical filtering", status: "ERROR", error: error.message })
	}

	// Test 6: Verify oldContent and newContent identical filtering
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasOldNewFiltering =
			content.includes("oldContent === trimmedNewContent") &&
			content.includes("oldContent and newContent are identical") &&
			content.includes('suggestion.type === "modify"')

		if (hasOldNewFiltering) {
			results.passed++
			results.tests.push({ name: "OldContent and newContent identical filtering", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "OldContent and newContent identical filtering", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "OldContent and newContent identical filtering", status: "ERROR", error: error.message })
	}

	// Test 7: Verify HTML tag regex pattern
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasTagRegex =
			content.includes("new RegExp(`<${tagName}\\\\b[^>]*>`, 'i')") && content.includes("tagRegex = new RegExp")

		if (hasTagRegex) {
			results.passed++
			results.tests.push({ name: "HTML tag regex pattern", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "HTML tag regex pattern", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "HTML tag regex pattern", status: "ERROR", error: error.message })
	}

	// Test 8: Verify attribute validation in tag content
	results.total++
	try {
		const uiProviderPath = path.join(__dirname, "..", "NextEditUIProvider.ts")
		const content = fs.readFileSync(uiProviderPath, "utf8")

		const hasAttrValidation =
			content.includes("attrRegex.test(tagContent)") &&
			content.includes("if (!attrRegex.test(tagContent))") &&
			content.includes("return { isMatch: false")

		if (hasAttrValidation) {
			results.passed++
			results.tests.push({ name: "Attribute validation in tag content", status: "PASS" })
		} else {
			results.failed++
			results.tests.push({ name: "Attribute validation in tag content", status: "FAIL" })
		}
	} catch (error) {
		results.failed++
		results.tests.push({ name: "Attribute validation in tag content", status: "ERROR", error: error.message })
	}

	// Calculate success rate
	const successRate = (results.passed / results.total) * 100

	// Print results
	console.log("📊 Highlight Range and Content Filtering Validation Results:")
	console.log("============================================================")

	results.tests.forEach((test) => {
		const icon = test.status === "PASS" ? "✅" : test.status === "FAIL" ? "❌" : "⚠️"
		console.log(`${icon} ${test.name}`)
		if (test.error) {
			console.log(`   Error: ${test.error}`)
		}
	})

	console.log("\n📈 Summary:")
	console.log(`   Total Tests: ${results.total}`)
	console.log(`   Passed: ${results.passed}`)
	console.log(`   Failed: ${results.failed}`)
	console.log(`   Success Rate: ${successRate.toFixed(1)}%`)

	const success = successRate >= 85 // 85% threshold
	console.log(`\n🎯 Result: ${success ? "🎉 FIXES IMPLEMENTED" : "⚠️ NEEDS MORE WORK"}`)

	if (success) {
		console.log("\n✨ Highlight range and content filtering fixes have been successfully implemented!")
		console.log("\n🎨 Highlight Range Improvements:")
		console.log("   1. ✅ Complete HTML tag matching")
		console.log("      - Extracts tag name from anchor pattern")
		console.log("      - Finds complete tag in document")
		console.log("      - Validates all specified attributes exist")
		console.log("      - Returns range for entire matching tag")
		console.log("")
		console.log("   2. ✅ Multiple attribute support")
		console.log('      - Handles patterns like: div id="x" class="y"')
		console.log("      - Validates each attribute individually")
		console.log("      - Ensures all attributes are present")
		console.log("")
		console.log("🔍 Content Filtering Improvements:")
		console.log("   1. ✅ Identical anchor and new_content filtering")
		console.log("      - Detects when anchor === new_content")
		console.log("      - Discards suggestions with no actual change")
		console.log("")
		console.log("   2. ✅ Identical oldContent and newContent filtering")
		console.log('      - Applies to "modify" type suggestions')
		console.log("      - Detects when oldContent === newContent")
		console.log("      - Prevents unnecessary modifications")
		console.log("")
		console.log("🎯 Example Scenarios Fixed:")
		console.log('   ✅ Anchor: "div id=\\"globe-container\\" class=\\"globe-container\\""')
		console.log('      → Now highlights entire <div id="globe-container" class="globe-container">')
		console.log('      → Previously only highlighted id="globe-container"')
		console.log("")
		console.log('   ✅ Anchor: "function test()" + new_content: "function test()"')
		console.log("      → Now filtered out as identical content")
		console.log("      → Previously would show useless suggestion")
		console.log("")
		console.log('   ✅ oldContent: "const x = 1" + newContent: "const x = 1"')
		console.log("      → Now filtered out as no actual change")
		console.log("      → Previously would show modify suggestion with no change")
		console.log("")
		console.log("🔍 Debug Log Examples:")
		console.log("   🎨 NextEdit: Matched using strategy: html_tag_match")
		console.log("   📍 NextEdit: Match position: 5-45 (complete tag range)")
		console.log("   ⚠️ NextEdit: Discarding suggestion - anchor and new_content are identical")
		console.log("   ⚠️ NextEdit: Discarding suggestion - oldContent and newContent are identical")
	} else {
		console.log("\n🔧 Some highlight range and content filtering features need additional work.")
	}

	return success
}

// Run validation
if (require.main === module) {
	validateHighlightAndContentFiltering()
}

module.exports = { validateHighlightAndContentFiltering }
