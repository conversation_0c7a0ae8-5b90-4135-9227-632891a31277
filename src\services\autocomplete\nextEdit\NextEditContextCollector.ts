import * as vscode from "vscode"
import { NextEditContext, NextEditConfig } from "./types/NextEditTypes"

/**
 * Represents a complete editing session
 */
interface EditingSession {
	startTime: number
	endTime: number
	originalContent: string
	finalContent: string
	filePath: string
}

/**
 * Document change tracking information
 */
interface DocumentChangeInfo {
	editingSessions: EditingSession[]
	lastModified: number
	currentSessionStart?: number
	pendingChanges: boolean
	originalContentSnapshot?: string
}

/**
 * Collects context information for Next Edit suggestions
 */
export class NextEditContextCollector {
	private config: NextEditConfig
	private documentChangeTracker: Map<string, DocumentChangeInfo> = new Map()
	private originalContentMap: Map<string, any> = new Map() // Store both content and timeouts
	private documentContentCache: Map<string, string> = new Map() // Cache document content

	constructor(config: NextEditConfig) {
		this.config = config
		this.setupChangeTracking()
	}

	/**
	 * Setup document change tracking
	 */
	private setupChangeTracking(): void {
		// Cache document content when opened
		vscode.workspace.onDidOpenTextDocument((document) => {
			if (document.uri.scheme === "file") {
				this.documentContentCache.set(document.uri.fsPath, document.getText())
			}
		})

		// Track document changes for recent changes detection
		vscode.workspace.onDidChangeTextDocument((event) => {
			if (event.document.uri.scheme === "file") {
				this.trackDocumentChanges(event)
			}
		})

		// Track when user stops typing to finalize editing sessions
		vscode.workspace.onDidSaveTextDocument((document) => {
			if (document.uri.scheme === "file") {
				this.finalizeEditingSession(document.uri.fsPath, document.getText())
			}
		})

		// Cache content for currently open documents
		vscode.window.visibleTextEditors.forEach((editor) => {
			if (editor.document.uri.scheme === "file") {
				this.documentContentCache.set(editor.document.uri.fsPath, editor.document.getText())
			}
		})
	}

	/**
	 * Track document changes for recent changes detection
	 */
	private trackDocumentChanges(event: vscode.TextDocumentChangeEvent): void {
		const filePath = event.document.uri.fsPath
		const timestamp = Date.now()

		// Get or create change info for this document
		let changeInfo = this.documentChangeTracker.get(filePath)
		if (!changeInfo) {
			changeInfo = {
				editingSessions: [],
				lastModified: timestamp,
				pendingChanges: false,
			}
			this.documentChangeTracker.set(filePath, changeInfo)
		}

		// Start a new editing session if needed
		if (!changeInfo.currentSessionStart && !changeInfo.pendingChanges) {
			changeInfo.currentSessionStart = timestamp
			changeInfo.pendingChanges = true

			// Use cached content as original content, or current content if not cached
			const originalContent = this.documentContentCache.get(filePath) || event.document.getText()
			changeInfo.originalContentSnapshot = originalContent

			console.log(`🔍 NextEdit: Starting new editing session for ${filePath}`)
			console.log(`📄 Original content length: ${originalContent.length}`)
		}

		changeInfo.lastModified = timestamp

		// Auto-finalize session after 3 seconds of inactivity
		this.scheduleSessionFinalization(filePath, event.document)
	}

	/**
	 * Schedule session finalization after inactivity
	 */
	private scheduleSessionFinalization(filePath: string, document: vscode.TextDocument): void {
		// Clear existing timeout
		const timeoutKey = `${filePath}_timeout`
		const existingTimeout = this.originalContentMap.get(timeoutKey)
		if (existingTimeout) {
			clearTimeout(existingTimeout as NodeJS.Timeout)
		}

		// Set new timeout
		const timeout = setTimeout(() => {
			this.finalizeEditingSession(filePath, document.getText())
		}, 3000) // 3 seconds of inactivity

		this.originalContentMap.set(timeoutKey, timeout as any)
	}

	/**
	 * Finalize editing session when user saves or stops typing
	 */
	private finalizeEditingSession(filePath: string, finalContent: string): void {
		const changeInfo = this.documentChangeTracker.get(filePath)
		if (!changeInfo || !changeInfo.currentSessionStart || !changeInfo.originalContentSnapshot) {
			return
		}

		const originalContent = changeInfo.originalContentSnapshot

		// Only create session if there are actual changes
		if (originalContent !== finalContent) {
			// Create editing session record
			const session: EditingSession = {
				startTime: changeInfo.currentSessionStart,
				endTime: Date.now(),
				originalContent,
				finalContent,
				filePath,
			}

			changeInfo.editingSessions.push(session)

			console.log(`✅ NextEdit: Finalized editing session for ${filePath}`)
			console.log(`📊 Changes: ${originalContent.length} → ${finalContent.length} chars`)
			console.log(`🔄 Diff preview: ${this.generateUnifiedDiff(originalContent, finalContent).substring(0, 100)}...`)
		}

		// Reset session state
		changeInfo.currentSessionStart = undefined
		changeInfo.pendingChanges = false
		changeInfo.originalContentSnapshot = undefined

		// Update cache with current content
		this.documentContentCache.set(filePath, finalContent)

		// Keep only recent sessions (last 30 minutes)
		const thirtyMinutesAgo = Date.now() - 30 * 60 * 1000
		changeInfo.editingSessions = changeInfo.editingSessions.filter((session) => session.endTime > thirtyMinutesAgo)

		// Limit to last 5 sessions
		if (changeInfo.editingSessions.length > 5) {
			changeInfo.editingSessions = changeInfo.editingSessions.slice(-5)
		}
	}

	/**
	 * Generate recent changes in diff format using editing sessions
	 */
	private generateRecentChangesDiff(filePath: string): string {
		const changeInfo = this.documentChangeTracker.get(filePath)
		if (!changeInfo) {
			return ""
		}

		const diffLines: string[] = []

		// Include current pending session if exists
		if (changeInfo.pendingChanges && changeInfo.currentSessionStart && changeInfo.originalContentSnapshot) {
			// Get current document content
			const currentDocument = vscode.window.visibleTextEditors.find(
				(editor) => editor.document.uri.fsPath === filePath,
			)?.document

			if (currentDocument) {
				const currentContent = currentDocument.getText()
				const originalContent = changeInfo.originalContentSnapshot

				if (originalContent !== currentContent) {
					const timeAgo = this.formatTimeAgo(Date.now() - changeInfo.currentSessionStart)
					diffLines.push(`\n--- Current Editing Session (${timeAgo} ago) ---`)

					const diff = this.generateUnifiedDiff(originalContent, currentContent)
					if (diff) {
						diffLines.push(diff)
						console.log(`🔄 NextEdit: Including current session diff for ${filePath}`)
					}
				}
			}
		}

		// Include completed sessions
		if (changeInfo.editingSessions.length > 0) {
			const recentSessions = changeInfo.editingSessions.slice(-2) // Last 2 completed sessions

			recentSessions.forEach((session, index) => {
				const timeAgo = this.formatTimeAgo(Date.now() - session.endTime)
				diffLines.push(`\n--- Editing Session ${index + 1} (${timeAgo} ago) ---`)

				// Generate unified diff between original and final content
				const diff = this.generateUnifiedDiff(session.originalContent, session.finalContent)
				if (diff) {
					diffLines.push(diff)
				} else {
					diffLines.push("(No significant changes)")
				}
			})
		}

		const result = diffLines.join("\n")
		if (result) {
			console.log(`📋 NextEdit: Generated recent changes diff for ${filePath}: ${result.length} chars`)
		}

		return result
	}

	/**
	 * Generate unified diff between two text contents
	 */
	private generateUnifiedDiff(originalContent: string, finalContent: string): string {
		const originalLines = originalContent.split("\n")
		const finalLines = finalContent.split("\n")

		// Use a simple but effective diff algorithm
		const diffLines: string[] = []
		let originalIndex = 0
		let finalIndex = 0

		while (originalIndex < originalLines.length || finalIndex < finalLines.length) {
			const originalLine = originalLines[originalIndex]
			const finalLine = finalLines[finalIndex]

			if (originalIndex >= originalLines.length) {
				// Only final lines left - all additions
				diffLines.push(`+${finalLine}`)
				finalIndex++
			} else if (finalIndex >= finalLines.length) {
				// Only original lines left - all deletions
				diffLines.push(`-${originalLine}`)
				originalIndex++
			} else if (originalLine === finalLine) {
				// Lines are the same - skip (or include for context if needed)
				originalIndex++
				finalIndex++
			} else {
				// Lines are different - check if it's a modification or insertion/deletion
				const nextOriginalMatch = finalLines.slice(finalIndex + 1).indexOf(originalLine)
				const nextFinalMatch = originalLines.slice(originalIndex + 1).indexOf(finalLine)

				if (nextOriginalMatch !== -1 && (nextFinalMatch === -1 || nextOriginalMatch < nextFinalMatch)) {
					// Original line appears later in final - this is an insertion
					diffLines.push(`+${finalLine}`)
					finalIndex++
				} else if (nextFinalMatch !== -1) {
					// Final line appears later in original - this is a deletion
					diffLines.push(`-${originalLine}`)
					originalIndex++
				} else {
					// Lines are different and don't appear later - this is a modification
					diffLines.push(`-${originalLine}`)
					diffLines.push(`+${finalLine}`)
					originalIndex++
					finalIndex++
				}
			}
		}

		// Limit diff output to prevent overwhelming display
		if (diffLines.length > 20) {
			const truncated = diffLines.slice(0, 20)
			truncated.push(`... (${diffLines.length - 20} more lines)`)
			return truncated.join("\n")
		}

		return diffLines.join("\n")
	}

	/**
	 * Format time ago in human readable format
	 */
	private formatTimeAgo(milliseconds: number): string {
		const seconds = Math.floor(milliseconds / 1000)
		if (seconds < 60) {
			return `${seconds}s`
		}

		const minutes = Math.floor(seconds / 60)
		if (minutes < 60) {
			return `${minutes}m`
		}

		const hours = Math.floor(minutes / 60)
		return `${hours}h`
	}

	/**
	 * Collect context for generating Next Edit suggestions
	 */
	async collectContext(
		document: vscode.TextDocument,
		position?: vscode.Position,
		recentChanges?: string,
	): Promise<NextEditContext> {
		const filePath = document.uri.fsPath
		const language = document.languageId

		// Get current code content (with context window for large files)
		const currentCode = this.extractContextualCode(document, position)

		// Get recent changes in diff format
		const recentChangesDiff = recentChanges || this.generateRecentChangesDiff(filePath)

		// Get error/warning context
		const errorsWarnings = await this.getErrorWarningContext(document)

		// Build allowed task types string
		const allowedTaskTypes = this.config.allowedTaskTypes.join(",")

		return {
			filePath,
			language,
			recentChanges: recentChangesDiff,
			currentCode,
			cursorPosition: position,
			errorsWarnings,
			allowedTaskTypes,
		}
	}

	/**
	 * Extract contextual code from document, limiting to context window for large files
	 */
	private extractContextualCode(document: vscode.TextDocument, position?: vscode.Position): string {
		const totalLines = document.lineCount

		// If file is small, return entire content
		if (totalLines <= this.config.contextWindowSize * 2) {
			return document.getText()
		}

		// For large files, extract context window around position
		const centerLine = position ? position.line : Math.floor(totalLines / 2)
		const startLine = Math.max(0, centerLine - this.config.contextWindowSize)
		const endLine = Math.min(totalLines - 1, centerLine + this.config.contextWindowSize)

		const startPos = new vscode.Position(startLine, 0)
		const endPos = new vscode.Position(endLine, document.lineAt(endLine).text.length)
		const range = new vscode.Range(startPos, endPos)

		const contextCode = document.getText(range)

		// Add markers to indicate truncation
		let result = ""
		if (startLine > 0) {
			result += `// ... (${startLine} lines above)\n`
		}
		result += contextCode
		if (endLine < totalLines - 1) {
			result += `\n// ... (${totalLines - endLine - 1} lines below)`
		}

		return result
	}

	/**
	 * Get error and warning context from diagnostics
	 */
	private async getErrorWarningContext(document: vscode.TextDocument): Promise<string> {
		try {
			const diagnostics = vscode.languages.getDiagnostics(document.uri)
			if (!diagnostics || diagnostics.length === 0) {
				return ""
			}

			const errors = diagnostics.filter((d) => d.severity === vscode.DiagnosticSeverity.Error)
			const warnings = diagnostics.filter((d) => d.severity === vscode.DiagnosticSeverity.Warning)

			let result = ""
			if (errors.length > 0) {
				result += "Errors:\n"
				errors.forEach((error) => {
					result += `- Line ${error.range.start.line + 1}: ${error.message}\n`
				})
			}

			if (warnings.length > 0) {
				if (result) {
					result += "\n"
				}
				result += "Warnings:\n"
				warnings.forEach((warning) => {
					result += `- Line ${warning.range.start.line + 1}: ${warning.message}\n`
				})
			}

			return result
		} catch (error) {
			console.warn("Failed to get error/warning context:", error)
			return ""
		}
	}

	/**
	 * Update configuration
	 */
	updateConfig(config: NextEditConfig): void {
		this.config = config
	}
}
