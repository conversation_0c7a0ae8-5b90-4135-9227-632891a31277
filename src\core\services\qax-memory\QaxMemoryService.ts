import * as vscode from "vscode"
import { QaxMemoryManager } from "./QaxMemoryManager"
import { QaxMemoryConfig } from "./qax-memory-types"
import { Api<PERSON>and<PERSON> } from "@api/index"
import { Logger } from "@services/logging/Logger"

/**
 * Qax Memory Service for managing user input memories
 * Uses singleton pattern similar to AuthService
 */
export class QaxMemoryService {
	private static instance: QaxMemoryService | null = null
	private memoryManager: QaxMemoryManager | null = null
	private isEnabled: boolean = true
	private disposables: vscode.Disposable[] = []
	private cachedWorkspaceRoot: string | null = null
	private workspaceRootCacheTime: number = 0
	private readonly CACHE_DURATION = 30000 // 30 seconds cache
	private getWorkspaceRoot: (() => Promise<string>) | null = null

	private constructor(private context: vscode.ExtensionContext) {
		this.loadConfiguration()
		this.setupConfigurationWatcher()
		this.setupWorkspaceWatcher()
	}

	/**
	 * Get singleton instance of QaxMemoryService
	 */
	public static getInstance(context?: vscode.ExtensionContext): QaxMemoryService {
		if (!QaxMemoryService.instance && context) {
			QaxMemoryService.instance = new QaxMemoryService(context)
		}
		return QaxMemoryService.instance!
	}

	/**
	 * Get existing instance without creating new one
	 */
	public static getInstanceIfExists(): QaxMemoryService | null {
		return QaxMemoryService.instance
	}

	/**
	 * Initialize with API handler and workspace root getter (called from Controller.initTask)
	 */
	public initializeWithApiHandler(apiHandler: ApiHandler, getWorkspaceRoot: () => Promise<string>): void {
		this.getWorkspaceRoot = getWorkspaceRoot
		this.initializeMemoryManager(apiHandler).catch((error) => {
			Logger.error("Failed to initialize memory manager:", error)
		})
	}

	/**
	 * Initialize memory manager with API handler
	 */
	private async initializeMemoryManager(apiHandler: ApiHandler): Promise<void> {
		if (!this.isEnabled || !this.getWorkspaceRoot) {
			return
		}

		try {
			const workspaceRoot = await this.getCachedWorkspaceRoot()
			if (!workspaceRoot) {
				return
			}

			const config = this.getMemoryConfig()
			this.memoryManager = new QaxMemoryManager(workspaceRoot, apiHandler, config)
		} catch (error) {
			Logger.error("Failed to initialize memory manager:", error)
			this.memoryManager = null
		}
	}

	/**
	 * Get workspace root with caching
	 */
	private async getCachedWorkspaceRoot(): Promise<string> {
		if (!this.getWorkspaceRoot) {
			throw new Error("Workspace root getter not initialized")
		}

		const now = Date.now()

		// Return cached value if still valid
		if (this.cachedWorkspaceRoot && now - this.workspaceRootCacheTime < this.CACHE_DURATION) {
			return this.cachedWorkspaceRoot
		}

		// Get fresh value and cache it
		try {
			const workspaceRoot = await this.getWorkspaceRoot()
			this.cachedWorkspaceRoot = workspaceRoot
			this.workspaceRootCacheTime = now
			return workspaceRoot
		} catch (error) {
			Logger.error("Failed to get workspace root:", error)
			throw error
		}
	}

	/**
	 * Load configuration from VS Code settings
	 */
	private loadConfiguration(): void {
		const config = vscode.workspace.getConfiguration("qax-codegen.memory")
		this.isEnabled = config.get("enabled", true)
	}

	/**
	 * Setup configuration change watcher
	 */
	private setupConfigurationWatcher(): void {
		const disposable = vscode.workspace.onDidChangeConfiguration((event) => {
			if (event.affectsConfiguration("qax-codegen.memory")) {
				this.loadConfiguration()
				if (this.memoryManager) {
					const config = this.getMemoryConfig()
					this.memoryManager.updateConfig(config)
				}
			}
		})
		this.disposables.push(disposable)
	}

	/**
	 * Setup workspace change watcher
	 */
	private setupWorkspaceWatcher(): void {
		const disposable = vscode.workspace.onDidChangeWorkspaceFolders(() => {
			// Clear cached workspace root when workspace changes
			this.cachedWorkspaceRoot = null
			this.workspaceRootCacheTime = 0
		})
		this.disposables.push(disposable)
	}

	/**
	 * Get memory configuration from VS Code settings
	 */
	private getMemoryConfig(): QaxMemoryConfig {
		const config = vscode.workspace.getConfiguration("qax-codegen.memory")
		return {
			enabled: config.get("enabled", true),
			minInputLength: config.get("minInputLength", 10),
			maxEntriesPerCategory: config.get("maxEntriesPerCategory", 20),
			confidenceThreshold: config.get("confidenceThreshold", 0.6),
			debounceMs: config.get("debounceMs", 2000),
		}
	}

	/**
	 * Process user input for memory extraction
	 */
	public processUserInput(input: string, context?: string): void {
		if (!this.isEnabled || !this.memoryManager) {
			return
		}

		// Process in background without blocking
		this.memoryManager.processUserInput(input, context).catch((error) => {
			Logger.error("Failed to process user input for memory:", error)
		})
	}

	/**
	 * Check if the service is ready to process input
	 */
	public isReady(): boolean {
		return this.isEnabled && this.memoryManager !== null
	}

	/**
	 * Get all memories
	 */
	public getAllMemories() {
		return this.memoryManager?.getAllMemories() || {}
	}

	/**
	 * Get memories by category
	 */
	public getMemoriesByCategory(category: string) {
		return this.memoryManager?.getMemoriesByCategory(category) || []
	}

	/**
	 * Clear all memories
	 */
	public async clearMemories(): Promise<void> {
		if (this.memoryManager) {
			await this.memoryManager.clearMemories()
		}
	}

	/**
	 * Enable/disable the service
	 */
	public setEnabled(enabled: boolean): void {
		this.isEnabled = enabled
		if (!enabled && this.memoryManager) {
			this.memoryManager.dispose()
			this.memoryManager = null
		}
	}

	/**
	 * Static method to initialize memory service with API handler if available
	 * This encapsulates all initialization logic within the service
	 */
	public static initializeWithApiHandlerIfAvailable(
		getApiHandler: () => any | null,
		getWorkspaceRoot: () => Promise<string>,
		outputChannel?: { appendLine: (message: string) => void },
	): void {
		try {
			const apiHandler = getApiHandler()
			if (!apiHandler) {
				return
			}

			const service = QaxMemoryService.getInstanceIfExists()
			if (service) {
				service.initializeWithApiHandler(apiHandler, async () => {
					try {
						const workspaceRoot = await getWorkspaceRoot()
						if (!workspaceRoot) {
							const message = "🧠 Memory: No workspace detected, memory functionality disabled"
							if (outputChannel) {
								outputChannel.appendLine(message)
							}
							return ""
						}
						return workspaceRoot
					} catch (error) {
						const message = "🧠 Memory: Failed to get workspace root, memory functionality disabled"
						if (outputChannel) {
							outputChannel.appendLine(message)
						}
						return ""
					}
				})
			}
		} catch (error) {
			console.error("🧠 QaxMemoryService: Failed to initialize with API handler:", error)
		}
	}

	/**
	 * Static method to process user input if service is available and ready
	 * This is a convenience method for use in controllers
	 */
	public static processUserInputIfReady(text: string, context?: string): void {
		const service = QaxMemoryService.getInstanceIfExists()
		if (service && service.isReady()) {
			service.processUserInput(text, context)
		}
	}

	/**
	 * Dispose the service and cleanup resources
	 */
	public dispose(): void {
		// Dispose memory manager
		if (this.memoryManager) {
			this.memoryManager.dispose()
			this.memoryManager = null
		}

		// Dispose VS Code event listeners
		this.disposables.forEach((disposable) => disposable.dispose())
		this.disposables = []

		// Clear cached data
		this.cachedWorkspaceRoot = null
		this.workspaceRootCacheTime = 0
		this.getWorkspaceRoot = null

		// Clear singleton instance
		QaxMemoryService.instance = null
	}
}
