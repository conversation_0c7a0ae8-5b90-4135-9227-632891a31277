import { SVGProps } from "react"

const QaxLogoWhite = (props: SVGProps<SVGSVGElement>) => (
	<svg xmlns="http://www.w3.org/2000/svg" width="50" height="50" viewBox="0 0 50 50" fill="none" {...props}>
		{/* 豹子主体轮廓 */}
		<path
			d="M8 15C8 12 10 10 13 10H20C21 8 23 6 25 6C27 6 29 8 30 10H37C40 10 42 12 42 15V18L45 22C45.5 23 45.5 24 45 25L42 29V32C42 35 40 37 37 37H35C34 39 32 41 30 41H20C18 41 16 39 15 37H13C10 37 8 35 8 32V29L5 25C4.5 24 4.5 23 5 22L8 18V15Z"
			fill="white"
		/>
		{/* 豹子斑点 */}
		<circle cx="18" cy="20" r="2" fill="#FFD700" />
		<circle cx="32" cy="20" r="2" fill="#FFD700" />
		<circle cx="15" cy="28" r="1.5" fill="#FFD700" />
		<circle cx="25" cy="25" r="1" fill="#FFD700" />
		<circle cx="35" cy="28" r="1.5" fill="#FFD700" />
		{/* 代码符号装饰 */}
		<path
			d="M12 32L14 30L12 28M38 28L36 30L38 32M22 33H28"
			stroke="#60A5FA"
			strokeWidth="1.5"
			strokeLinecap="round"
			strokeLinejoin="round"
		/>
		{/* Codegen 文字 */}
		<text x="25" y="46" textAnchor="middle" fontSize="8" fontWeight="600" fill="#60A5FA">
			Codegen
		</text>
	</svg>
)
export default QaxLogoWhite
