import { useExtensionState } from "@/context/ExtensionStateContext"
import { useQaxAuth } from "@/context/ClineAuthContext"
import { ModelsServiceClient } from "@/services/grpc-client"
import { OpenAiModelsRequest } from "@shared/proto/models"
import { VSCodeTextField } from "@vscode/webview-ui-toolkit/react"
import React, { useEffect, useState } from "react"
import { useMount } from "react-use"
import { ModelInfoView } from "@/components/settings/common/ModelInfoView"
import { normalizeApiConfiguration } from "@/components/settings/utils/providerUtils"
import { useApiConfigurationHandlers } from "@/components/settings/utils/useApiConfigurationHandlers"
import { Mode } from "@shared/ChatSettings"

export interface QaxCodegenModelPickerProps {
	isPopup?: boolean
	currentMode: Mode
}

const QaxCodegenModelPicker: React.FC<QaxCodegenModelPickerProps> = ({ isPopup, currentMode }) => {
	const { handleFieldsChange } = useApiConfigurationHandlers()
	const { apiConfiguration } = useExtensionState()
	const { qaxUser } = useQaxAuth()

	// 根据当前模式获取对应的模型ID
	const getCurrentModelId = () => {
		return currentMode === "plan"
			? apiConfiguration?.planModeQaxCodegenModelId || ""
			: apiConfiguration?.actModeQaxCodegenModelId || ""
	}

	const [searchTerm, setSearchTerm] = useState(getCurrentModelId())
	const [availableModels, setAvailableModels] = useState<string[]>([])
	const [isLoading, setIsLoading] = useState(false)

	const handleModelChange = (newModelId: string) => {
		setSearchTerm(newModelId)
		// 根据当前模式更新对应的字段
		const updateFields =
			currentMode === "plan" ? { planModeQaxCodegenModelId: newModelId } : { actModeQaxCodegenModelId: newModelId }
		handleFieldsChange(updateFields)
	}

	const { selectedModelId, selectedModelInfo } = normalizeApiConfiguration(apiConfiguration, currentMode)

	// Fetch models when user is logged in
	const fetchModels = async () => {
		if (!qaxUser) {
			setAvailableModels([])
			return
		}

		setIsLoading(true)
		try {
			const response = await ModelsServiceClient.getQaxCodegenModels(OpenAiModelsRequest.create({}))
			const models = response.values || []
			setAvailableModels(models)

			// 如果当前没有选择模型且有可用模型，自动选择第一个
			if (!searchTerm && models.length > 0) {
				const defaultModel = models[0]
				setSearchTerm(defaultModel)
				const updateFields =
					currentMode === "plan"
						? { planModeQaxCodegenModelId: defaultModel }
						: { actModeQaxCodegenModelId: defaultModel }
				handleFieldsChange(updateFields)
			}
		} catch (error) {
			console.error("Failed to fetch QAX Codegen models:", error)
			setAvailableModels([])
		} finally {
			setIsLoading(false)
		}
	}

	// Fetch models on mount and when user changes
	useMount(() => {
		if (qaxUser) {
			fetchModels()
		}
	})

	useEffect(() => {
		fetchModels()
	}, [qaxUser])

	// Sync external changes
	useEffect(() => {
		const currentModelId = getCurrentModelId()
		setSearchTerm(currentModelId)
	}, [apiConfiguration?.planModeQaxCodegenModelId, apiConfiguration?.actModeQaxCodegenModelId, currentMode])

	if (!qaxUser) {
		return (
			<div
				style={{
					padding: "10px",
					backgroundColor: "var(--vscode-inputValidation-warningBackground)",
					border: "1px solid var(--vscode-inputValidation-warningBorder)",
					borderRadius: "3px",
					marginBottom: 10,
				}}>
				<p style={{ margin: 0, color: "var(--vscode-inputValidation-warningForeground)" }}>
					请先登录 QAX 账户以使用 QAX Codegen 服务
				</p>
			</div>
		)
	}

	return (
		<div style={{ width: "100%" }}>
			<div style={{ display: "flex", flexDirection: "column" }}>
				<label htmlFor="qax-codegen-model-search">
					<span style={{ fontWeight: 500 }}>Model</span>
				</label>

				<div style={{ position: "relative" }}>
					<VSCodeTextField
						id="qax-codegen-model-search"
						style={{ width: "100%", marginTop: 5 }}
						value={searchTerm}
						placeholder={isLoading ? "Loading models..." : "Please select a model from the list below"}
						onInput={(e: any) => {
							const value = e.target.value
							setSearchTerm(value)
							handleModelChange(value)
						}}
					/>
				</div>

				{availableModels.length > 0 && (
					<div style={{ marginTop: 10 }}>
						<p
							style={{
								fontSize: "12px",
								margin: "0 0 5px 0",
								color: "var(--vscode-descriptionForeground)",
							}}>
							Available models ({availableModels.length}):
						</p>
						<div
							style={{
								maxHeight: "150px",
								overflowY: "auto",
								border: "1px solid var(--vscode-widget-border)",
								borderRadius: "3px",
							}}>
							{availableModels.map((model) => (
								<div
									key={model}
									style={{
										padding: "8px 12px",
										cursor: "pointer",
										backgroundColor:
											selectedModelId === model
												? "var(--vscode-list-activeSelectionBackground)"
												: "transparent",
										color:
											selectedModelId === model
												? "var(--vscode-list-activeSelectionForeground)"
												: "var(--vscode-foreground)",
										borderBottom: "1px solid var(--vscode-widget-border)",
									}}
									onClick={() => handleModelChange(model)}
									onMouseEnter={(e) => {
										if (selectedModelId !== model) {
											e.currentTarget.style.backgroundColor = "var(--vscode-list-hoverBackground)"
										}
									}}
									onMouseLeave={(e) => {
										if (selectedModelId !== model) {
											e.currentTarget.style.backgroundColor = "transparent"
										}
									}}>
									{model}
								</div>
							))}
						</div>
					</div>
				)}

				{!isLoading && availableModels.length === 0 && qaxUser && (
					<p
						style={{
							fontSize: "12px",
							marginTop: 5,
							color: "var(--vscode-errorForeground)",
						}}>
						No models available. Please check your QAX account permissions.
					</p>
				)}

				<p
					style={{
						fontSize: "12px",
						marginTop: 5,
						color: "var(--vscode-descriptionForeground)",
					}}>
					QAX Codegen models are automatically fetched based on your account permissions.
				</p>

				{selectedModelId && selectedModelInfo && (
					<ModelInfoView selectedModelId={selectedModelId} modelInfo={selectedModelInfo} isPopup={isPopup} />
				)}
			</div>
		</div>
	)
}

export default QaxCodegenModelPicker
