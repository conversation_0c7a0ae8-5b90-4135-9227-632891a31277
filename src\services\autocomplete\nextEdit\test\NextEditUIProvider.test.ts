import * as vscode from "vscode"
import { NextEditUIProvider } from "../NextEditUIProvider"
import { NextEditSuggestion } from "../types/NextEditTypes"

/**
 * Validation script for NextEditUIProvider improvements
 *
 * This script validates the following improvements:
 * 1. UI immediate dismissal after button clicks
 * 2. Code diff visualization highlighting
 * 3. Enhanced hover content with actual code diffs
 * 4. Improved Next button functionality
 * 5. Enhanced Explain button functionality
 */

export function validateNextEditUIImprovements(): boolean {
	console.log("🧪 Starting NextEditUIProvider validation...")

	let allTestsPassed = true
	const results: string[] = []

	try {
		const uiProvider = new NextEditUIProvider()

		// Test 1: Verify new decoration types exist
		const hasNewDecorations = validateNewDecorationTypes(uiProvider)
		results.push(`✅ New decoration types: ${hasNewDecorations ? "PASS" : "FAIL"}`)
		if (!hasNewDecorations) allTestsPassed = false

		// Test 2: Verify code diff generation
		const codeDiffWorks = validateCodeDiffGeneration(uiProvider)
		results.push(`✅ Code diff generation: ${codeDiffWorks ? "PASS" : "FAIL"}`)
		if (!codeDiffWorks) allTestsPassed = false

		// Test 3: Verify language detection
		const languageDetection = validateLanguageDetection(uiProvider)
		results.push(`✅ Language detection: ${languageDetection ? "PASS" : "FAIL"}`)
		if (!languageDetection) allTestsPassed = false

		// Test 4: Verify impact analysis
		const impactAnalysis = validateImpactAnalysis(uiProvider)
		results.push(`✅ Impact analysis: ${impactAnalysis ? "PASS" : "FAIL"}`)
		if (!impactAnalysis) allTestsPassed = false

		uiProvider.dispose()
	} catch (error) {
		console.error("❌ Validation failed with error:", error)
		allTestsPassed = false
		results.push(`❌ Validation error: ${error}`)
	}

	// Print results
	console.log("\n📊 Validation Results:")
	results.forEach((result) => console.log(`  ${result}`))
	console.log(`\n🎯 Overall Result: ${allTestsPassed ? "✅ ALL TESTS PASSED" : "❌ SOME TESTS FAILED"}`)

	return allTestsPassed
}

function validateNewDecorationTypes(uiProvider: NextEditUIProvider): boolean {
	try {
		// Check if new decoration types exist
		const hasAddDecoration = (uiProvider as any).addLineDecorationType !== undefined
		const hasModifyDecoration = (uiProvider as any).modifyLineDecorationType !== undefined
		const hasDeleteDecoration = (uiProvider as any).deleteLineDecorationType !== undefined

		return hasAddDecoration && hasModifyDecoration && hasDeleteDecoration
	} catch (error) {
		console.error("Error validating decoration types:", error)
		return false
	}
}

function validateCodeDiffGeneration(uiProvider: NextEditUIProvider): boolean {
	try {
		const addSuggestion = createTestSuggestion("add")
		const modifySuggestion = createTestSuggestion("modify")
		const deleteSuggestion = createTestSuggestion("delete")

		// Test code diff generation
		const addDiff = (uiProvider as any).generateCodeDiff(addSuggestion)
		const modifyDiff = (uiProvider as any).generateCodeDiff(modifySuggestion)
		const deleteDiff = (uiProvider as any).generateCodeDiff(deleteSuggestion)

		const addDiffValid = addDiff.includes("```diff") && addDiff.includes("+ const newVariable = 'test'")
		const modifyDiffValid = modifyDiff.includes("```typescript") || modifyDiff.includes("```diff")
		const deleteDiffValid = deleteDiff.includes("Content will be deleted") || deleteDiff.includes("```diff")

		return addDiffValid && modifyDiffValid && deleteDiffValid
	} catch (error) {
		console.error("Error validating code diff generation:", error)
		return false
	}
}

function validateLanguageDetection(uiProvider: NextEditUIProvider): boolean {
	try {
		const testCases = [
			{ path: "/test/file.ts", expected: "typescript" },
			{ path: "/test/file.js", expected: "javascript" },
			{ path: "/test/file.py", expected: "python" },
			{ path: "/test/file.java", expected: "java" },
			{ path: "/test/file.unknown", expected: "text" },
		]

		return testCases.every(({ path, expected }) => {
			const detected = (uiProvider as any).getLanguageFromFilePath(path)
			return detected === expected
		})
	} catch (error) {
		console.error("Error validating language detection:", error)
		return false
	}
}

function validateImpactAnalysis(uiProvider: NextEditUIProvider): boolean {
	try {
		const addSuggestion = createTestSuggestion("add")
		const modifySuggestion = createTestSuggestion("modify")
		const deleteSuggestion = createTestSuggestion("delete")

		const addImpact = (uiProvider as any).generateImpactAnalysis(addSuggestion)
		const modifyImpact = (uiProvider as any).generateImpactAnalysis(modifySuggestion)
		const deleteImpact = (uiProvider as any).generateImpactAnalysis(deleteSuggestion)

		const addValid = addImpact.includes("New functionality will be added")
		const modifyValid = modifyImpact.includes("Existing code will be updated")
		const deleteValid = deleteImpact.includes("Selected code will be permanently removed")

		return addValid && modifyValid && deleteValid
	} catch (error) {
		console.error("Error validating impact analysis:", error)
		return false
	}
}

// Helper function to create test suggestions
function createTestSuggestion(type: "add" | "modify" | "delete"): NextEditSuggestion {
	return {
		id: `test-${type}-${Date.now()}`,
		type: type as any,
		description: `Test ${type} suggestion`,
		location: {
			anchor: "function test()",
			position: "after",
		},
		patch: {
			newContent: "const newVariable = 'test'",
		},
		reasoning: `This is a test ${type} suggestion for validation`,
		filePath: "/test/file.ts",
		createdAt: new Date(),
	}
}
