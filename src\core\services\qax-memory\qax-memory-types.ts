/**
 * Qax Memory entry representing a classified and summarized user input
 */
export interface QaxMemoryEntry {
	id: string
	category: string // Dynamic category based on topic/theme
	summary: string
	originalInput: string
	timestamp: number
	confidence: number // 0-1, confidence in classification
}

/**
 * Qax Memory classification result from AI analysis
 */
export interface QaxMemoryClassificationResult {
	category: string // Dynamic category based on topic/theme
	summary: string
	confidence: number
	reasoning?: string
}

/**
 * Configuration for Qax memory generation
 */
export interface QaxMemoryConfig {
	enabled: boolean
	minInputLength: number // Minimum input length to process
	maxEntriesPerCategory: number // Maximum entries to keep per category
	confidenceThreshold: number // Minimum confidence to save entry
	debounceMs: number // Debounce time for processing
}

/**
 * Qax Memory storage structure for the markdown file - now supports dynamic categories
 */
export interface QaxMemoryStorage {
	[category: string]: QaxMemoryEntry[]
}

/**
 * Qax Memory update operation types
 */
export enum QaxMemoryUpdateOperation {
	ADD = "add",
	UPDATE = "update",
	REMOVE = "remove",
	MERGE = "merge",
}

/**
 * Qax Memory update instruction from AI model
 */
export interface QaxMemoryUpdateInstruction {
	operation: QaxMemoryUpdateOperation
	category: string // Dynamic category based on topic/theme
	targetId?: string // For update/remove operations
	newEntry?: {
		summary: string
		confidence: number
		reasoning?: string
	}
	mergeWith?: string[] // IDs to merge with
}

/**
 * Complete Qax memory update result from AI analysis
 */
export interface QaxMemoryUpdateResult {
	shouldUpdate: boolean
	instructions: QaxMemoryUpdateInstruction[]
	reasoning: string
	newEntryClassification?: QaxMemoryClassificationResult
}

/**
 * Default Qax memory configuration
 */
export const QAX_DEFAULT_MEMORY_CONFIG: QaxMemoryConfig = {
	enabled: true,
	minInputLength: 10,
	maxEntriesPerCategory: 20,
	confidenceThreshold: 0.6,
	debounceMs: 2000,
}
