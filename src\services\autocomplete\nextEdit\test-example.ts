// Test file for Next Edit functionality
// Make some changes to this file to trigger Next Edit suggestions
//
// TESTING INSTRUCTIONS:
// 1. Make some edits to this file (add/remove/modify code)
// 2. Save the file to finalize the editing session
// 3. Position your cursor somewhere in the code
// 4. Next Edit should show a floating panel with suggestions
// 5. Press ESC to close the panel

export class TestClass {
	private name: string

	constructor(name: string) {
		this.name = name
	}

	// Add a method here - Next Edit should suggest error handling, validation, etc.
	public getName() {
		return this.name
	}

	// Incomplete method - Next Edit should suggest completion
	public setName(newName: string) {
		// TODO: Add validation
		this.name = newName
	}
}

// Function without error handling - Next Edit should suggest improvements
export function processData(data: any) {
	return data.toString()
}

// Async function without proper error handling
export async function fetchData(url: string) {
	const response = await fetch(url)
	return response.json()
}

// Test recent changes detection:
// 1. Make some changes to this file (add/modify/delete code)
// 2. Wait 3 seconds for the editing session to auto-finalize
// 3. Position cursor somewhere and wait for Next Edit suggestions
// 4. The hover should show meaningful diff of your recent changes

// Example: Try adding this function and then test:
export function testFunction() {
	console.log("This is a test function for Next Edit")
	return "test"
}
