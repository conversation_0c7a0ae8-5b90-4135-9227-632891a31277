/**
 * Qax Todo 系统的类型定义
 * 统一管理 Qax Todo 功能相关的所有类型和接口
 */

// Qax Todo 状态类型 - 匹配后端数据格式
export type QaxTodoStatus = "pending" | "in_progress" | "completed"

// Qax Todo 项接口 - 匹配后端数据结构
export interface QaxTodoItem {
	id: string
	content: string
	status: QaxTodoStatus
}

// Qax Todo 列表组件 Props
export interface QaxTodoListProps {
	todos?: QaxTodoItem[]
	onTodoUpdate?: (todos: QaxTodoItem[]) => void
}

// Qax Todo 项组件 Props
export interface QaxTodoItemComponentProps {
	todo: QaxTodoItem
	onStatusChange: (id: string) => void
	onDelete: (id: string) => void
}

// Qax Todo 统计信息
export interface QaxTodoStats {
	completed: number
	total: number
}
